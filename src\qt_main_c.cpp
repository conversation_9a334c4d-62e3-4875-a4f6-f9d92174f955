#include <QCoreApplication>
#include <QTextCodec>
#include <cstdio>
#include <cstring>
#include <cmath>
#include "mainwindow.h"
#include "qapplication.h"
#include "workmodel.h"
#include "qt_main_c.h"

// 全局
static ThreatEvaluator* g_threat_evaluator = NULL;
static RKNNInference* g_rknn_inference = NULL;
static JammingDecision* g_jamming_decision = NULL;
RadarParameters g_radar_params;
std::atomic<bool>  paraFlag;
char frame_buffer[1024];

// 存储最新结果
ThreatAssessmentData g_latest_threat_data;
JammingDecisionResult g_latest_jamming_result;

RKNNOutput* g_current_rknn_output = NULL;

// 模拟雷达输入数据
void simulate_radar_input(RadarParameters* radar, int scenario) {
    const char* scenario_names[] = {
        "高威胁场景（终末制导）", "中等威胁场景（跟踪雷达）", "高频场景（火控雷达）",
        "低威胁场景（搜索雷达）", "极低威胁场景（静默雷达）"
    };

    switch (scenario) {
        case 0: // 高威胁场景（终末制导雷达）
            radar->frequency_mhz = 8000.0;      // X波段
            radar->pulse_width_us = 2.0;        // 短脉冲
            radar->prt_us = 2000.0;             // 中等PRF
            radar->power_w = 500000.0;          // 高功率
            radar->distance_km = 25.0;          // 近距离（l1范围内）
            radar->speed_ms = 250.0;            // 高速接近
            radar->direction_deg = 10.0;        // 正面接近
            radar->work_mode = 4;               // 终末制导模式

            break;

        case 1: // 中等威胁场景（跟踪雷达）
            radar->frequency_mhz = 5000.0;      // C波段
            radar->pulse_width_us = 5.0;        // 中等脉冲
            radar->prt_us = 5000.0;             // 低PRF
            radar->power_w = 200000.0;          // 中等功率
            radar->distance_km = 120.0;         // 中等距离
            radar->speed_ms = 100.0;            // 中等速度
            radar->direction_deg = 45.0;        // 侧面接近
            radar->work_mode = 2;               // 跟踪模式

            break;

        case 2: // 高频场景（火控雷达）
            radar->frequency_mhz = 12000.0;     // X波段高频
            radar->pulse_width_us = 0.5;        // 极短脉冲
            radar->prt_us = 800.0;              // 高PRF
            radar->power_w = 2000000.0;         // 极高功率
            radar->distance_km = 30.0;          // 近距离
            radar->speed_ms = 350.0;            // 超高速
            radar->direction_deg = 5.0;         // 正面接近
            radar->work_mode = 3;               // 制导模式

            break;

        case 3: // 低威胁场景（搜索雷达）
            radar->frequency_mhz = 3000.0;      // S波段
            radar->pulse_width_us = 10.0;       // 长脉冲
            radar->prt_us = 10000.0;            // 极低PRF
            radar->power_w = 50000.0;           // 低功率
            radar->distance_km = 250.0;         // 远距离
            radar->speed_ms = 50.0;             // 低速
            radar->direction_deg = 120.0;       // 斜后方
            radar->work_mode = 1;               // 搜索模式

            break;

        case 4: // 极低威胁场景（静默雷达）
            radar->frequency_mhz = 1500.0;      // L波段
            radar->pulse_width_us = 20.0;       // 极长脉冲
            radar->prt_us = 15000.0;            // 极低PRF
            radar->power_w = 10000.0;           // 极低功率
            radar->distance_km = 350.0;         // 极远距离
            radar->speed_ms = 20.0;             // 极低速
            radar->direction_deg = 160.0;       // 背离
            radar->work_mode = 0;               // 静默模式

            break;
    }

    // 简化输出：只显示场景名称
    printf("场景: %s\n", scenario_names[scenario]);
}

// 显示决策结果
void display_complete_result(const ThreatAssessmentData* threat_data,
                           const JammingDecisionResult* jamming_result) {

    printf("威胁等级: %d, ", threat_data->threat_level);

    if (jamming_result->jamming_count > 0) {
        printf("干扰决策: 启动");
        if (jamming_result->use_combination) {
            printf("(组合干扰)");
        }
        printf("\n");

        // 只显示混合格式输出
        output_mixed_jamming_format(threat_data, jamming_result);
    } else {
        printf("干扰决策: 不启动\n");
    }
}


// 输出干扰参数
void output_mixed_jamming_format(const ThreatAssessmentData* threat_data,
                                const JammingDecisionResult* jamming_result) {
    // 生成混合格式数据
    MixedData mixed_output[128];
    int output_size = generate_standard_mixed_output(threat_data, jamming_result,
                                                    g_current_rknn_output, mixed_output, 128);

    printf("混合格式输出:\n");
    printf("数据长度: %d 项\n", output_size);
    printf("混合输出: [");
    for (int i = 0; i < output_size; i++) {
        if (mixed_output[i].type == MixedData::TYPE_INT) {
            printf("%d", mixed_output[i].value.int_val);
        } else {
            printf("%.3f", mixed_output[i].value.float_val);
        }
        if (i < output_size - 1) printf(", ");
    }
    printf("]\n");
}

// 生成标准化数据输出
int generate_standard_mixed_output(const ThreatAssessmentData* threat_data,
                                  const JammingDecisionResult* jamming_result,
                                  const RKNNOutput* rknn_output,
                                  MixedData* output, int max_size) {
    if (!threat_data || !jamming_result || !output || max_size < 2) {
        return 0;
    }

    int idx = 0;

    // [0] 威胁等级 (int)
    output[idx].type = MixedData::TYPE_INT;
    output[idx].value.int_val = threat_data->threat_level;
    idx++;

    // [1] 干扰数量 (int)
    output[idx].type = MixedData::TYPE_INT;
    output[idx].value.int_val = jamming_result->jamming_count;
    idx++;

    // 如果有干扰，添加每种干扰的详细参数
    if (jamming_result->jamming_count > 0) {
        for (int i = 0; i < jamming_result->jamming_count && i < 8; i++) {
            if (idx + 2 >= max_size) break;

            // 干扰编号 (int)
            output[idx].type = MixedData::TYPE_INT;
            output[idx].value.int_val = i;
            idx++;

            // 干扰类型ID (int, 转换为0-4的ID)
            int jamming_id = (int)jamming_result->jamming_types[i] - 1;  // 转换为0-based
            if (jamming_id < 0) jamming_id = 0;
            if (jamming_id > 4) jamming_id = 4;
            output[idx].type = MixedData::TYPE_INT;
            output[idx].value.int_val = jamming_id;
            idx++;

            // 根据干扰类型添加对应的参数
            idx = add_mixed_jamming_params_by_type(jamming_id, jamming_result->jamming_probs[i],
                                                  rknn_output, output, idx, max_size);
        }

        // 如果是组合干扰，在输出中标记
        if (jamming_result->jamming_count > 1) {
            printf("  生成组合干扰标准输出: %d种干扰类型\n", jamming_result->jamming_count);
        }
    }

    return idx;
}

// 根据干扰类型ID获取名称
const char* get_jamming_type_name_by_id(int jamming_id) {
    switch (jamming_id) {
        case 0: return "Comb";        // 梳状谱
        case 1: return "ISRJ";        // 间歇采样转发
        case 2: return "Broadband";   // 宽带阻塞噪声
        case 3: return "Smart";       // 灵巧噪声
        case 4: return "Deception";   // 拖引
        default: return "Unknown";
    }
}

// 根据带宽选择ID获取带宽描述（对应宽带噪声表格）
const char* get_bandwidth_description(int bandwidth_id) {
    static const char* bandwidth_table[] = {
        "输出直流",      // 0
        "5kHz",         // 1
        "10 kHz",       // 2
        "20 kHz",       // 3
        "50 kHz",       // 4
        "100 kHz",      // 5
        "300 kHz",      // 6
        "1 MHz",        // 7
        "10 MHz",       // 8
        "20 MHz",       // 9
        "50 MHz",       // 10
        "100 MHz",      // 11
        "200 MHz",      // 12
        "300 MHz",      // 13
        "400 MHz",      // 14
        "500 MHz",      // 15
        "600 MHz",      // 16
        "700 MHz",      // 17
        "800 MHz",      // 18
        "900 MHz",      // 19
        "1000 MHz"      // 20
    };

    if (bandwidth_id >= 0 && bandwidth_id <= 20) {
        return bandwidth_table[bandwidth_id];
    } else {
        return "未知带宽";
    }
}

// 根据干扰类型添加参数
int add_mixed_jamming_params_by_type(int jamming_id, double prob, const RKNNOutput* rknn_output,
                                    MixedData* output, int start_idx, int max_size) {
    int idx = start_idx;

    switch (jamming_id) {
        case 0: // 梳状谱
            {
                // 梳状谱个数 (int, 1-8)
                int comb_count = 1;
                if (rknn_output && rknn_output->output_size > 6) {
                    comb_count = (int)fabs(rknn_output->output_data[6]);
                    if (comb_count < 1) comb_count = 1;
                    if (comb_count > 8) comb_count = 8;
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_INT;
                    output[idx].value.int_val = comb_count;
                    idx++;
                }

                // 为每个梳状谱生成参数（频偏、闪烁周期、闪烁保持时间）
                for (int i = 0; i < comb_count && idx + 2 < max_size; i++) {
                    // 频偏1, 频偏2, ... (float, kHz)
                    float freq_offset = 50.0f + i * 20.0f;  // 默认值
                    if (rknn_output && rknn_output->output_size > 7 + i) {
                        freq_offset = fabs(rknn_output->output_data[7 + i]) * 500.0f + 10.0f;  // 10-510 kHz
                    }
                    if (freq_offset > 1000.0f) freq_offset = 1000.0f;
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = freq_offset;
                    idx++;

                    // 闪烁周期1, 闪烁周期2, ... (float, us)
                    float flicker_period = 20.0f + i * 5.0f;  // 默认值
                    if (rknn_output && rknn_output->output_size > 9 + i) {
                        flicker_period = fabs(rknn_output->output_data[9 + i]) * 80.0f + 10.0f;  // 10-90us
                    }
                    if (flicker_period > 100.0f) flicker_period = 100.0f;
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = flicker_period;
                    idx++;

                    // 闪烁保持时间1, 闪烁保持时间2, ... (float, us)
                    float hold_time = 15.0f + i * 3.0f;  // 默认值
                    if (rknn_output && rknn_output->output_size > 11 + i) {
                        hold_time = fabs(rknn_output->output_data[11 + i]) * 40.0f + 5.0f;  // 5-45us
                    }
                    if (hold_time > 50.0f) hold_time = 50.0f;
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = hold_time;
                    idx++;
                }
            }
            break;

        case 1: // 间歇采样转发
            {
                // 重复转发时间间隔 (float, us)
                float repeat_interval = 100.0f + prob * 200.0f;
                if (rknn_output && rknn_output->output_size > 12) {
                    repeat_interval = fabs(rknn_output->output_data[12]) * 200.0f + 50.0f;  // 50-250us
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = repeat_interval;
                    idx++;
                }

                // 间歇采样（切片）开关 (int, 0:off 1:on)
                int sampling_switch = (prob > 0.5) ? 1 : 0;
                if (rknn_output && rknn_output->output_size > 13) {
                    sampling_switch = (rknn_output->output_data[13] > 0.5f) ? 1 : 0;
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_INT;
                    output[idx].value.int_val = sampling_switch;
                    idx++;
                }

                // 间歇采样（切片）周期 (float, us)
                float sampling_period = 1.0f + prob * 5.0f;
                if (rknn_output && rknn_output->output_size > 14) {
                    sampling_period = fabs(rknn_output->output_data[14]) * 5.0f + 0.5f;  // 0.5-5.5us
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = sampling_period;
                    idx++;
                }

                // 间歇采样（切片）宽度 (float, us)
                float sampling_width = 0.5f + prob * 2.0f;
                if (rknn_output && rknn_output->output_size > 15) {
                    sampling_width = fabs(rknn_output->output_data[15]) * 2.0f + 0.3f;  // 0.3-2.3us
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = sampling_width;
                    idx++;
                }

                // 干扰覆盖距离范围 (float, m)
                float coverage_range = 10000.0f + prob * 40000.0f;  // 10-50km
                if (rknn_output && rknn_output->output_size > 16) {
                    coverage_range = fabs(rknn_output->output_data[16]) * 40000.0f + 5000.0f;  // 5-45km
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = coverage_range;
                    idx++;
                }

                // 脉冲采样长度 (float, us)
                float pulse_length = 0.8f + prob * 1.5f;
                if (rknn_output && rknn_output->output_size > 17) {
                    pulse_length = fabs(rknn_output->output_data[17]) * 2.0f + 0.5f;  // 0.5-2.5us
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = pulse_length;
                    idx++;
                }
            }
            break;

        case 2: // 宽带阻塞噪声
            {
                // 噪声带宽选择 (int, 见噪声带宽选择表格 0-20)
                int bandwidth_selection = (int)(prob * 20);
                if (rknn_output && rknn_output->output_size > 18) {
                    bandwidth_selection = (int)(fabs(rknn_output->output_data[18]) * 20);
                    if (bandwidth_selection > 20) bandwidth_selection = 20;
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_INT;
                    output[idx].value.int_val = bandwidth_selection;
                    idx++;
                }
            }
            break;

        case 3: // 灵巧噪声
            {
                // 噪声带宽选择 (int, 见噪声带宽选择表格 0-20)
                int bandwidth_selection = (int)(prob * 20);
                if (rknn_output && rknn_output->output_size > 19) {
                    bandwidth_selection = (int)(fabs(rknn_output->output_data[19]) * 20);
                    if (bandwidth_selection > 20) bandwidth_selection = 20;
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_INT;
                    output[idx].value.int_val = bandwidth_selection;
                    idx++;
                }

                // 噪声源选择 (int, 1：高斯噪声 2：多普勒闪烁 3：多普勒噪声)
                int noise_source = (prob > 0.7) ? 3 : ((prob > 0.4) ? 2 : 1);
                if (rknn_output && rknn_output->output_size > 20) {
                    float val = fabs(rknn_output->output_data[20]);
                    noise_source = (val > 0.67) ? 3 : ((val > 0.33) ? 2 : 1);
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_INT;
                    output[idx].value.int_val = noise_source;
                    idx++;
                }

                // 多普勒闪烁模式 (int, 1：固定闪烁 2：随机闪烁)
                int flicker_mode = (prob > 0.5) ? 2 : 1;
                if (rknn_output && rknn_output->output_size > 21) {
                    flicker_mode = (rknn_output->output_data[21] > 0.5f) ? 2 : 1;
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_INT;
                    output[idx].value.int_val = flicker_mode;
                    idx++;
                }

                // 闪烁保持时间 (float, us)
                float hold_time = 5.0f + prob * 20.0f;
                if (rknn_output && rknn_output->output_size > 22) {
                    hold_time = fabs(rknn_output->output_data[22]) * 50.0f + 5.0f;  // 5-55us
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = hold_time;
                    idx++;
                }

                // 闪烁消失时间 (float, us)
                float fade_time = 2.0f + prob * 18.0f;
                if (rknn_output && rknn_output->output_size > 23) {
                    fade_time = fabs(rknn_output->output_data[23]) * 20.0f + 2.0f;  // 2-22us
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = fade_time;
                    idx++;
                }

                // 多普勒噪声带宽 (float, kHz)
                float doppler_bandwidth = 10.0f + prob * 90.0f;
                if (rknn_output && rknn_output->output_size > 24) {
                    doppler_bandwidth = fabs(rknn_output->output_data[24]) * 100.0f + 10.0f;  // 10-110kHz
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = doppler_bandwidth;
                    idx++;
                }

                // 多普勒噪声跳变周期 (float, kHz)
                float hop_period = 5.0f + prob * 45.0f;
                if (rknn_output && rknn_output->output_size > 25) {
                    hop_period = fabs(rknn_output->output_data[25]) * 50.0f + 5.0f;  // 5-55kHz
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = hop_period;
                    idx++;
                }
            }
            break;

        case 4: // 拖引
            {
                // 速拖速度 (float, m/s)
                float velocity_drag_speed = 200.0f + prob * 300.0f;
                if (rknn_output && rknn_output->output_size > 26) {
                    velocity_drag_speed = fabs(rknn_output->output_data[26]) * 400.0f + 100.0f;  // 100-500m/s
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = velocity_drag_speed;
                    idx++;
                }

                // 速拖加速度 (float, m/s²)
                float velocity_drag_accel = 5.0f + prob * 45.0f;
                if (rknn_output && rknn_output->output_size > 27) {
                    velocity_drag_accel = fabs(rknn_output->output_data[27]) * 50.0f + 5.0f;  // 5-55m/s²
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = velocity_drag_accel;
                    idx++;
                }

                // 距拖速度 (float, m/s)
                float range_drag_speed = 10.0f + prob * 40.0f;
                if (rknn_output && rknn_output->output_size > 28) {
                    range_drag_speed = fabs(rknn_output->output_data[28]) * 50.0f + 10.0f;  // 10-60m/s
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = range_drag_speed;
                    idx++;
                }

                // 距拖加速度 (float, m/s²)
                float range_drag_accel = 2.0f + prob * 18.0f;
                if (rknn_output && rknn_output->output_size > 29) {
                    range_drag_accel = fabs(rknn_output->output_data[29]) * 20.0f + 2.0f;  // 2-22m/s²
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = range_drag_accel;
                    idx++;
                }

                // 捕获时间 (float, s)
                float capture_time = 0.5f + prob * 4.5f;
                if (rknn_output && rknn_output->output_size > 30) {
                    capture_time = fabs(rknn_output->output_data[30]) * 5.0f + 0.5f;  // 0.5-5.5s
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = capture_time;
                    idx++;
                }

                // 拖引时间 (float, s)
                float drag_time = 2.0f + prob * 8.0f;
                if (rknn_output && rknn_output->output_size > 31) {
                    drag_time = fabs(rknn_output->output_data[31]) * 10.0f + 2.0f;  // 2-12s
                }
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = drag_time;
                    idx++;
                }

                // 保持时间 (float, s)
                float hold_time = 1.0f + prob * 4.0f;  // 1-5s
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = hold_time;
                    idx++;
                }

                // 消失时间 (float, s)
                float fade_time = 0.5f + prob * 2.5f;  // 0.5-3.0s
                if (idx < max_size) {
                    output[idx].type = MixedData::TYPE_FLOAT;
                    output[idx].value.float_val = fade_time;
                    idx++;
                }
            }
            break;

        default:
            break;
    }

    return idx;
}




// 运行完整测试场景
void run_complete_test_scenario( ) {

    //  模拟雷达输入
    RadarParameters radar_params;
   simulate_radar_input(&radar_params, 1);

//    while(paraFlag)
//    {
//        QThread::msleep(1);
//    }
    paraFlag = 1;
    memcpy(&radar_params, &g_radar_params, sizeof(RadarParameters));
    paraFlag = 0;

    //  威胁评估
    ThreatAssessmentData threat_data;

    // 添加调试信息：打印雷达参数
    qDebug() << "=== 雷达参数调试信息 ===";
    qDebug() << "频率 (MHz):" << radar_params.frequency_mhz;
    qDebug() << "脉宽 (μs):" << radar_params.pulse_width_us;
    qDebug() << "PRT (μs):" << radar_params.prt_us;
    qDebug() << "功率 (W):" << radar_params.power_w;
    qDebug() << "距离 (km):" << radar_params.distance_km;
    qDebug() << "速度 (m/s):" << radar_params.speed_ms;
    qDebug() << "方向 (度):" << radar_params.direction_deg;
    qDebug() << "工作模式:" << radar_params.work_mode;

    int threat_result = threat_evaluator_assess(g_threat_evaluator, &radar_params, &threat_data);

    // 添加调试信息：打印威胁评估详细结果
    qDebug() << "=== 威胁评估详细结果 ===";
    qDebug() << "威胁等级:" << threat_data.threat_level;
    qDebug() << "威胁值:" << threat_data.threat_value;
    qDebug() << "速度威胁:" << threat_data.speed_threat;
    qDebug() << "距离威胁:" << threat_data.distance_threat;
    qDebug() << "方向威胁:" << threat_data.direction_threat;
    qDebug() << "PRF威胁:" << threat_data.prf_threat;
    qDebug() << "频率威胁:" << threat_data.frequency_threat;
    qDebug() << "脉宽威胁:" << threat_data.pulse_width_threat;
    qDebug() << "模式威胁:" << threat_data.mode_threat;
    qDebug() << "功率威胁:" << threat_data.power_threat;
    // 3. 准备RKNN输入
    RKNNInput rknn_input;

    // 准备12维特征向量
    rknn_input.input_data[0] = (float)(radar_params.frequency_mhz / 10000.0);    // 频率归一化
    rknn_input.input_data[1] = (float)(radar_params.pulse_width_us / 10.0);      // 脉宽归一化
    rknn_input.input_data[2] = (float)(radar_params.prt_us / 10000.0);           // PRT归一化
    rknn_input.input_data[3] = (float)(radar_params.power_w / 1000000.0);        // 功率归一化
    rknn_input.input_data[4] = (float)(radar_params.distance_km / 300.0);        // 距离归一化
    rknn_input.input_data[5] = (float)(radar_params.speed_ms / 1000.0);          // 速度归一化
    rknn_input.input_data[6] = (float)(radar_params.direction_deg / 360.0);      // 方向归一化
    rknn_input.input_data[7] = (float)(radar_params.work_mode / 4.0);            // 工作模式归一化
    rknn_input.input_data[8] = (float)threat_data.threat_value;                  // 威胁值
    rknn_input.input_data[9] = (float)(threat_data.threat_level / 5.0);          // 威胁等级归一化
    rknn_input.input_data[10] = (float)threat_data.confidence;                   // 威胁置信度
    rknn_input.input_data[11] = (float)threat_data.priority;                     // 威胁优先级
    rknn_input.input_size = 12;

    //  RKNN推理
    RKNNOutput rknn_output;
    int rknn_result = rknn_inference_predict(g_rknn_inference, &rknn_input, &rknn_output);


    g_current_rknn_output = &rknn_output;

    //  干扰决策
    JammingDecisionResult jamming_result;
    int jamming_decision_result = jamming_decision_decide(g_jamming_decision, threat_data.threat_level,
                                                         threat_data.threat_value, &rknn_output,
                                                         radar_params.work_mode, &jamming_result);

//    if (jamming_decision_result != 0) {
//        printf("错误: 干扰决策失败，错误码: %d\n", jamming_decision_result);
//        g_current_rknn_output = NULL;  // 清理全局指针
//        rknn_free_output(&rknn_output);
//        return;
//    }

    // 显示完整结果
//    display_complete_result(&threat_data, &jamming_result);

    //  保存结果到全局变量
    memcpy(&g_latest_threat_data, &threat_data, sizeof(ThreatAssessmentData));
    memcpy(&g_latest_jamming_result, &jamming_result, sizeof(JammingDecisionResult));

    //  清理
    g_current_rknn_output = NULL;
    rknn_free_output(&rknn_output);
}


// 发送标准格式数据的函数
void send_mixed_format_data() {
    // 生成标准格式数据
    MixedData mixed_output[128];
    int output_size = generate_standard_mixed_output(&g_latest_threat_data, &g_latest_jamming_result,
                                                    g_current_rknn_output, mixed_output, 128);

    if (output_size > 0) {
        // farme
        int dat_bytes = output_size * 4;
        int total_bytes=dat_bytes+sizeof (FrameHead);
        memset(frame_buffer,0,1024);
        FrameHead *head=(FrameHead*)frame_buffer;
        head->head=0x5a5a5a5a;
        head->len=dat_bytes;

        char* data_buffer =frame_buffer+sizeof (FrameHead);
        int byte_index = 0;
        for (int i = 0; i < output_size; i++) {
            if (mixed_output[i].type == MixedData::TYPE_INT) {
                memcpy(&data_buffer[byte_index], &mixed_output[i].value.int_val, 4);
            } else {
                memcpy(&data_buffer[byte_index], &mixed_output[i].value.float_val, 4);
            }
            byte_index += 4;
        }

//        for (int i = 0; i < output_size; i++) {
//            if (mixed_output[i].type == MixedData::TYPE_INT) {
//                qDebug()<<mixed_output[i].value.int_val;
////                printf("%d", mixed_output[i].value.int_val);
//            } else {
//                qDebug()<<mixed_output[i].value.int_val;
//               // printf("%.3f", mixed_output[i].value.float_val);
//            }
//            if (i < output_size - 1) printf(", ");
//        }
//        printf("]\n");

        // 通过信号发送数据 - 使用正确的函数签名
        extern globalMannager g_;  // 声明外部全局变量
        emit g_.sig_datasend_s(frame_buffer, (uint)total_bytes);

//        free(frame_buffer);
//        printf("   [OK] 标准格式数据发送完成\n");
    } else {
//        printf("[ERROR] 生成标准格式数据失败\n");
    }
}



//初始化
int initialize_all_modules(const char* model_path) {
    // 初始化威胁评估器（启用雷达平台评估，l1=30km, l2=300km）
    g_threat_evaluator = threat_evaluator_create_with_params(1, 30.0, 200.0);
    if (!g_threat_evaluator) {
        // 威胁评估器初始化失败
        return -1;
    }

    // 初始化RKNN推理
    g_rknn_inference = rknn_inference_create(model_path);
    if (!g_rknn_inference) {
        // RKNN推理器初始化失败
        threat_evaluator_destroy(g_threat_evaluator);
        return -2;
    }

    // 初始化干扰决策器
    g_jamming_decision = jamming_decision_create();
    if (!g_jamming_decision) {
        // 干扰决策器初始化失败
        rknn_inference_destroy(g_rknn_inference);
        threat_evaluator_destroy(g_threat_evaluator);
        return -3;
    }

    // 所有核心模块初始化成功
    return 0;
}

void destroy_all_modules(void) {
    if (g_jamming_decision) {
        jamming_decision_destroy(g_jamming_decision);
        g_jamming_decision = NULL;
    }

    if (g_rknn_inference) {
        rknn_inference_destroy(g_rknn_inference);
        g_rknn_inference = NULL;
    }

    if (g_threat_evaluator) {
        threat_evaluator_destroy(g_threat_evaluator);
        g_threat_evaluator = NULL;
    }
}





