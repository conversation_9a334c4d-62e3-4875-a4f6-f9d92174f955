/*******************************************************************************************
 * FileProperties: 
 *     FileName: workmodel.cpp
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2334/DEVELOP/P2/5_Software/51_Host/branches/stgyModule/workmodel/workmodel.cpp $
 *         $Author: huangpeixuan $
 *         $Revision: 1718 $
 *         $Date: 2025-07-16 19:00:20 $
*******************************************************************************************/
#include "workmodel.h"

globalMannager g_;

///**—————————————————————————————————线程池管理类———————————————————————————————————————**///

QThread *MythreadPool::ThreadPool_TakeThread()
{
    if(wm_threadPool_free.isEmpty())
        ThreadPool_init();

    return wm_threadPool_free.takeFirst();
}

ERRORET MythreadPool::ThreadPool_recycleThread(QThread *thread)
{
    wm_threadPool_free.append(thread);

    return SUCCESS;
}

ERRORET MythreadPool::ThreadPool_init(quint64 num)
{
    while(num > 0)
    {
        QThread *tmpthread = new QThread();
        wm_threadPool.append(tmpthread);
        wm_threadPool_free.append(tmpthread);
        --num;
    }

    return SUCCESS;
}

ERRORET MythreadPool::ThreadPool_delete()
{
    foreach(QThread *threadp, wm_threadPool)
    {
        if(threadp->isRunning())
        {
            threadp->terminate();
            threadp->wait();
        }
        delete threadp;
        threadp = NULL;
    }

    return SUCCESS;
}

///**——————————————————————————————————工作模块——————————————————————————————————————**///

Workmodel::Workmodel()
{
    wm_tcpserver = new MyTcpServer();
    connect(wm_tcpserver, SIGNAL(sig_sendsocket(qintptr)), this, SLOT(slot_tcp_newconnect(qintptr)));

    wm_udpsocket = new QUdpSocket(this);
}

Workmodel::~Workmodel()
{
    Net_tcpserver_close();
    Net_tcpclient_close();
    delete wm_tcpserver;
}

ERRORET Workmodel::Process_run(QString exename)
{
    Process_close(exename);

    QProcess *tmpprocess = new QProcess();
    tmpprocess->start(exename);

    wm_processMap.insert(exename, tmpprocess);

    return SUCCESS;
}

ERRORET Workmodel::Process_close(QString exename)
{
    if(wm_processMap.contains(exename))
    {
        QProcess *tmpprocess0 = wm_processMap.value(exename);

        wm_processMap.value(exename)->close();
        wm_processMap.remove(exename);

        delete(tmpprocess0);
        tmpprocess0 = NULL;
    }
    else
    {
        QString process = QString("taskkill /im ") + exename + QString(" /f");
        QProcess::startDetached(process);
    }

    return SUCCESS;
}

ERRORET Workmodel::Net_tcpserver_init(const QHostAddress &address, quint16 port)
{
    if(wm_tcpserver->isListening()) return COMM_NET_ISLISTENING;
    if(!wm_tcpserver->listen(address, port)) return COMM_NET_LISTEN_FAILED;

    return SUCCESS;
}

ERRORET Workmodel::Net_tcpserver_close()
{
    for(QMap<QPair<QString, quint16>, TcpSocketMannager *>::iterator it = wm_tcpsocketMap.begin();\
        it != wm_tcpsocketMap.end();\
        ++it)
    {
        delete it.value();
        it.value() = NULL;
    }
    wm_tcpsocketMap.clear();
    wm_tcpserver->close();

    return SUCCESS;
}

ERRORET Workmodel::Net_tcpclient_init(const QHostAddress &address, quint16 port)
{
    QPair<QString, quint16> tmpPeerKey(address.toString(), port);
    if(wm_tcpsocketMap_c.contains(tmpPeerKey)) return COMM_NET_ALREADYCONNECT;

    TcpSocketMannager *tmptcpsocket = new TcpSocketMannager(NULL, g_.threadPool.ThreadPool_TakeThread(), address, port);
    connect(tmptcpsocket->m_tcpobject, SIGNAL(sig_disconnect(QHostAddress,quint16,SOCKETMODE)),
            this, SLOT(slot_tcp_disconnect(QHostAddress,quint16,SOCKETMODE)));
    connect(tmptcpsocket->m_tcpobject, SIGNAL(sig_connectFailed(QHostAddress,quint16,SOCKETMODE)),
            this, SLOT(slot_tcp_disconnect(QHostAddress,quint16,SOCKETMODE)));

    wm_tcpsocketMap_c.insert(tmpPeerKey, tmptcpsocket);

    return SUCCESS;
}

ERRORET Workmodel::Net_tcpclient_close()
{
    for(QMap<QPair<QString, quint16>, TcpSocketMannager *>::iterator it = wm_tcpsocketMap_c.begin();\
        it != wm_tcpsocketMap_c.end();\
        ++it)
    {
        delete it.value();
        it.value() = NULL;
    }
    wm_tcpsocketMap_c.clear();

    return SUCCESS;
}

ERRORET Workmodel::Net_tcpclient_close(const QHostAddress &address, quint16 port)
{
    QPair<QString, quint16> tmpPeerKey(address.toString(), port);
    if(!wm_tcpsocketMap_c.contains(tmpPeerKey)) return COMM_NET_ALREADYDISCONNECT;

    delete wm_tcpsocketMap_c.value(tmpPeerKey);
    wm_tcpsocketMap_c.remove(tmpPeerKey);

    return SUCCESS;
}

ERRORET Workmodel::Net_tcpWrite_s2c(QString address, quint16 port, QByteArray &sendData)
{
    QPair<QString, quint16> tmpPeerKey(address, port);
    if(!wm_tcpsocketMap.contains(tmpPeerKey)) return COMM_NET_NUEXISTCONNECT;

    emit wm_tcpsocketMap.value(tmpPeerKey)->sig_sendData(sendData);

    return SUCCESS;
}

ERRORET Workmodel::Net_tcpWrite_c2s(QString address, quint16 port, QByteArray &sendData)
{
    QPair<QString, quint16> tmpPeerKey(address, port);
    if(!wm_tcpsocketMap_c.contains(tmpPeerKey)) return COMM_NET_NUEXISTCONNECT;

    emit wm_tcpsocketMap_c.value(tmpPeerKey)->sig_sendData(sendData);

    return SUCCESS;
}

ERRORET Workmodel::Net_Host_WakeUp(QString MacAddress)
{
    char wakeupData[102];  //首6 + Mac地址6 *16

    char head[6] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    QByteArray mac = QByteArray::fromHex(MacAddress.toStdString().c_str());

    memcpy(wakeupData, head, 6);
    for(int i = 0; i < 16; ++i){
        memcpy(wakeupData + 6 + i*6, mac.data(), 6);
    }

    quint64 res = wm_udpsocket->writeDatagram(wakeupData, 102, QHostAddress("***************"), 9);

    return res == -1 ? FAILED : SUCCESS;
}

ERRORET Workmodel::slot_tcp_newconnect(qintptr handle)
{
    TcpSocketMannager *tmptcpsocket = new TcpSocketMannager(handle, g_.threadPool.ThreadPool_TakeThread());

    QPair<QString, quint16> tmpPeerKey = tmptcpsocket->m_tcpobject->retPeerMsg();
    if(wm_tcpsocketMap.contains(tmpPeerKey)) return COMM_NET_ALREADYCONNECT;

    wm_tcpsocketMap.insert(tmpPeerKey, tmptcpsocket);
    connect(tmptcpsocket->m_tcpobject, SIGNAL(sig_disconnect(QHostAddress,quint16,SOCKETMODE)),
            this, SLOT(slot_tcp_disconnect(QHostAddress,quint16,SOCKETMODE)));

    return SUCCESS;
}

ERRORET Workmodel::slot_tcp_disconnect(QHostAddress peerAddr, quint16 port, SOCKETMODE type)
{
    QPair<QString, quint16> tmpPeerKey(peerAddr.toString(), port);

    if(type == SOCKET_S)
    {
        if(!wm_tcpsocketMap.contains(tmpPeerKey)) return COMM_NET_ALREADYDISCONNECT;

        delete wm_tcpsocketMap.value(tmpPeerKey);
        wm_tcpsocketMap.remove(tmpPeerKey);
    }
    else
    {
        if(!wm_tcpsocketMap_c.contains(tmpPeerKey)) return COMM_NET_ALREADYDISCONNECT;

        delete wm_tcpsocketMap_c.value(tmpPeerKey);
        wm_tcpsocketMap_c.remove(tmpPeerKey);
    }

    return SUCCESS;
}

ERRORET Ini_ui_state_save(QObject *father)
{
    QString filename = QString("../cfg/workmodel_ini/%1.ini").arg(father->objectName());

    QSettings ini(filename, QSettings::IniFormat);

    QList<QDoubleSpinBox *> dsbl = father->findChildren<QDoubleSpinBox *>();
    for(QDoubleSpinBox *dsb : dsbl)
    {
        ini.setValue(dsb->objectName(), dsb->value());
    }

    QList<QSpinBox *> sbl = father->findChildren<QSpinBox *>();
    for(QSpinBox *sb : sbl)
    {
        ini.setValue(sb->objectName(), sb->value());
    }

    QList<QComboBox *> cbbl = father->findChildren<QComboBox *>();
    for(QComboBox *cbb : cbbl)
    {
        ini.setValue(cbb->objectName(), cbb->currentIndex());
    }

    QList<QLineEdit *> lel = father->findChildren<QLineEdit *>();
    for(QLineEdit *le : lel)
    {
        if(le->objectName() == NULL) continue;
        ini.setValue(le->objectName(), le->text());
    }

    QList<QCheckBox *> cbl = father->findChildren<QCheckBox *>();
    for(QCheckBox *cb : cbl)
    {
        ini.setValue(cb->objectName(), cb->isChecked());
    }

    return SUCCESS;
}


ERRORET Ini_ui_state_load(QObject *father)
{
    QString filename = QString("../cfg/workmodel_ini/%1.ini").arg(father->objectName());

    QSettings ini(filename, QSettings::IniFormat);

    QList<QDoubleSpinBox *> dsbl = father->findChildren<QDoubleSpinBox *>();
    for(QDoubleSpinBox *dsb : dsbl)
    {
        dsb->setValue( ini.value(dsb->objectName()).toDouble() );
    }

    QList<QSpinBox *> sbl = father->findChildren<QSpinBox *>();
    for(QSpinBox *sb : sbl)
    {
        sb->setValue( ini.value(sb->objectName()).toInt() );
    }

    QList<QComboBox *> cbbl = father->findChildren<QComboBox *>();
    for(QComboBox *cbb : cbbl)
    {
        cbb->setCurrentIndex( ini.value(cbb->objectName()).toUInt() );
    }

    QList<QLineEdit *> lel = father->findChildren<QLineEdit *>();
    for(QLineEdit *le : lel)
    {
        if(le->objectName() == NULL) continue;
        le->setText( ini.value(le->objectName()).toString() );
    }

    QList<QCheckBox *> cbl = father->findChildren<QCheckBox *>();
    for(QCheckBox *cb : cbl)
    {
        cb->setChecked( ini.value(cb->objectName()).toBool() );
    }

    return SUCCESS;
}

ERRORET Ini_ui_state_save_splitter(QSplitter *father)
{
    QString filename = QString("../cfg/workmodel_ini/%1.ini").arg(father->objectName());

    QSettings ini(filename, QSettings::IniFormat);

    QList<int> sizeList = father->sizes();
    int i = 0;
    for(int s : sizeList)
    {
        ini.setValue(QString::number(i), s);
        ++i;
    }

    ini.setValue("index", i);

    return SUCCESS;
}


ERRORET Ini_ui_state_load_splitter(QSplitter *father)
{
    QString filename = QString("../cfg/workmodel_ini/%1.ini").arg(father->objectName());

    QSettings ini(filename, QSettings::IniFormat);

    QList<int> sizeList;
    int i = ini.value("index").toInt();

    for(int n = 0; n < i; ++n)
    {
        sizeList.append( ini.value(QString::number(n)).toInt() );
    }

    father->setSizes(sizeList);

    return SUCCESS;
}

ERRORET File_copy_cover(QString filepath_src, QString filepath_new)
{
    QFile file0(filepath_src);
    QFile file(filepath_new);

    if(!file0.exists()) return FILE_NOTEXISTS;

    if(file.exists()) file.remove();

    file0.copy(filepath_new);

    return SUCCESS;
}

ERRORET File_read_csv(QString filepath, QList<QStringList> &fdata)
{
    QFile file(filepath);

    if(!file.exists()) return FILE_NOTEXISTS;

    if(!file.open(QIODevice::ReadOnly | QIODevice::Text)) return FILE_OPENFAIL;

    /* 这种处理方式会导致每行的数据末尾都有换行符，这显然不是我们需要的
    while(!file.atEnd())
    {
        QString data = file.readLine();
        QStringList datal = data.split(',', QString::SkipEmptyParts);
        fdata.append(datal);
    }
    */

    /* 处理换行符（使用时注意区分不同换行符）
    while(!file.atEnd())
    {
        QString data0= file.readAll();
        QStringList data0l = data0.split('\n', QString::SkipEmptyParts);

        foreach (auto d, data0l) {
            QStringList datal = d.split(',', QString::SkipEmptyParts);
            fdata.append(datal);
        }
    }
    */

    // 使用文本流读取
    QTextStream out(&file);
    while(!out.atEnd())
    {
        QString data = out.readLine();
        QStringList datal = data.split(',', QString::SkipEmptyParts);
        fdata.append(datal);
    }

    file.close();

    return SUCCESS;
}

ERRORET Ui_text_alignment(QComboBox *cbb, Qt::Alignment alignment)
{
    cbb->setEditable(true);
    cbb->lineEdit()->setReadOnly(true);
    cbb->lineEdit()->setAlignment(alignment);

    for(int i = 0; i < cbb->model()->rowCount(); ++i)
    {
        if(Q_NULLPTR != dynamic_cast<QStandardItemModel *>(cbb->model())->item(i))
            dynamic_cast<QStandardItemModel *>(cbb->model())->item(i)->setTextAlignment(alignment);
    }

    return SUCCESS;
}

ERRORET Ui_text_alignment(QObject *father, Qt::Alignment alignment)
{
    QList<QDoubleSpinBox *> dsbl = father->findChildren<QDoubleSpinBox *>();
    for(QDoubleSpinBox *dsb : dsbl)
    {
        dsb->setAlignment(alignment);
    }

    QList<QSpinBox *> sbl = father->findChildren<QSpinBox *>();
    for(QSpinBox *sb : sbl)
    {
        sb->setAlignment(alignment);
    }

    QList<QComboBox *> cbbl = father->findChildren<QComboBox *>();
    for(QComboBox *cbb : cbbl)
    {
        cbb->setEditable(true);
        cbb->lineEdit()->setReadOnly(true);
        cbb->lineEdit()->setAlignment(alignment);

        for(int i = 0; i < cbb->model()->rowCount(); ++i)
        {
            if(Q_NULLPTR != dynamic_cast<QStandardItemModel *>(cbb->model())->item(i))
                dynamic_cast<QStandardItemModel *>(cbb->model())->item(i)->setTextAlignment(alignment);
        }
    }

    QList<QLineEdit *> lel = father->findChildren<QLineEdit *>();
    for(QLineEdit *le : lel)
    {
        if(le->objectName() == NULL) continue;
        le->setAlignment(alignment);
    }

    return SUCCESS;
}

ERRORET Ui_tips_show(QString title, QString text, QString styleSheet, QMessageBox::Icon icon)
{
    QMessageBox tmpTips;

    tmpTips.setWindowTitle(title);
    tmpTips.setText(text);
    tmpTips.setIcon(icon);
    tmpTips.setStyleSheet(styleSheet);

    tmpTips.exec();

    return SUCCESS;
}

///**———————————————————————————————————通信与套接字管理类—————————————————————————————————————**///

TcpObject::TcpObject()
{
    connect(this, SIGNAL(sig_delSelf()), this, SLOT(slot_delSelf()));
}

TcpObject::~TcpObject(){
    m_flagDel = true;
    emit sig_delSelf();
    while(m_flagDel){}
}

QHostAddress TcpObject::retPeerAddress(){
    while(m_peerAddr.toString() == "0.0.0.0"){
        //qDebug()<<"";
    }
    return m_peerAddr;
}

QPair<QString, quint16> TcpObject::retPeerMsg()
{
    while(m_peerAddr.toString() == "0.0.0.0" && m_peerPort == 0){
        //qDebug()<<"";
    }

    return QPair<QString, quint16>(m_peerAddr.toString(), m_peerPort);
}

void TcpObject::slot_createSocket(qintptr handle)
{
    if(m_tcpsocket != NULL) return;

    m_type = SOCKET_S;
    m_tcpsocket = new QTcpSocket();
    m_tcpsocket->setSocketDescriptor(handle);
    m_peerAddr = m_tcpsocket->peerAddress();
    m_peerPort = m_tcpsocket->peerPort();

    emit g_.sig_shownum(++g_.membernum);
    emit g_.sig_commState( m_peerAddr.toString(), m_tcpsocket->localPort(), COMM_NET_CONNTOC_SUCCESS);

    qDebug()<<QThread::currentThreadId()<<":"<<"socket CET_s";

    connect(m_tcpsocket, SIGNAL(readyRead()), this, SLOT(slot_tcpRecv()));
    connect(m_tcpsocket, SIGNAL(disconnected()), this, SLOT(slot_disconnect()));
    connect(&g_, SIGNAL(sig_datasend_s()), this, SLOT(slot_tcpWrite()));
    connect(&g_, SIGNAL(sig_datasend_s(char*,uint)), this, SLOT(slot_tcpWrite(char*,uint)));
    connect(&g_, SIGNAL(sig_datasend_s(QByteArray)), this, SLOT(slot_tcpWrite(QByteArray)));

    m_CommModule = CreatUnpuckModule(m_peerAddr);
    connect(m_CommModule, SIGNAL(sig_datasend(char*,uint)), this, SLOT(slot_tcpWrite(char*,uint)));
    m_CommModule->setAddress(m_peerAddr.toString());
}

void TcpObject::slot_createSocket(const QHostAddress &address, quint16 port)
{
    if(m_tcpsocket != NULL) return;

    m_type = SOCKET_C;
    m_tcpsocket = new QTcpSocket();
    m_tcpsocket->abort();
    m_tcpsocket->connectToHost(address, port);
    if(!m_tcpsocket->waitForConnected(5000))
    {
//        delete m_tcpsocket;
//        m_tcpsocket = NULL;
        emit sig_connectFailed(address, port, m_type);
        emit g_.sig_commState( address.toString(), port, COMM_NET_CONNTOS_FAILED);

        return;
    }
    emit g_.sig_commState( address.toString(), port, COMM_NET_CONNTOS_SUCCESS);
    qDebug()<<QThread::currentThreadId()<<":"<<"socket CET_c";
    m_peerAddr = m_tcpsocket->peerAddress();
    m_peerPort = m_tcpsocket->peerPort();

    connect(m_tcpsocket, SIGNAL(readyRead()), this, SLOT(slot_tcpRecv()));
    connect(m_tcpsocket, SIGNAL(disconnected()), this, SLOT(slot_disconnect()));
    connect(&g_, SIGNAL(sig_datasend_c()), this, SLOT(slot_tcpWrite()));
    connect(&g_, SIGNAL(sig_datasend_c(char*,uint)), this, SLOT(slot_tcpWrite(char*,uint)));
    connect(&g_, SIGNAL(sig_datasend_c(QByteArray)), this, SLOT(slot_tcpWrite(QByteArray)));

    m_CommModule = CreatUnpuckModule(address, port);
    connect(m_CommModule, SIGNAL(sig_datasend(char*,uint)), this, SLOT(slot_tcpWrite(char*,uint)));
    m_CommModule->setAddress(address.toString());
}

void TcpObject::slot_disconnect()
{
    if(m_flagDel) return;

    // 触发的销毁操作中部分处理会在运行slot_disconnect函数的线程运行,因此对象销毁不会发生在slot_disconnect函数完成前
    emit sig_disconnect(m_peerAddr, m_peerPort, m_type);

    if(m_type == SOCKET_C) emit g_.sig_commState( m_peerAddr.toString(), m_tcpsocket->peerPort(), COMM_NET_DISCONN_S);
    else if(m_type == SOCKET_S) emit g_.sig_commState( m_peerAddr.toString(), m_tcpsocket->localPort(), COMM_NET_DISCONN_C);

    emit g_.sig_shownum(--g_.membernum);
}

void TcpObject::slot_tcpRecv()
{
    QByteArray t_msg = m_tcpsocket->readAll();
    m_CommModule->unpackMassage(t_msg);
}

void TcpObject::slot_tcpWrite()
{

}

void TcpObject::slot_tcpWrite(char *data, uint size)
{

    m_tcpsocket->write(data, size);
    m_tcpsocket->flush();
}

void TcpObject::slot_tcpWrite(QByteArray sendData)
{

    m_tcpsocket->write(sendData);
    m_tcpsocket->flush();
}

void TcpObject::slot_delSelf()
{
    delete m_CommModule;

    if(m_tcpsocket != NULL)
    {
//        m_tcpsocket->blockSignals(true);
        m_tcpsocket->close();
//        m_tcpsocket->blockSignals(false);
        delete m_tcpsocket;
        m_tcpsocket = NULL;
    }
    qDebug()<<QThread::currentThreadId()<<":"<<"socket DEL1";

    m_flagDel = false;
}

///**——————————————————————————————————通信与套接字管理子类——————————————————————————————————————**///



///**——————————————————————————————————通信线程与事件管理类——————————————————————————————————————**///

TcpObject *CreatTcpObject(QHostAddress address, quint16 port)
{
    return new TcpObject();
}



TcpSocketMannager::TcpSocketMannager(qintptr handle, QThread *thread, const QHostAddress &address, quint16 port)
{
    qRegisterMetaType<QHostAddress>("QHostAddress");
    qRegisterMetaType<qintptr>("qintptr");
    qRegisterMetaType<SOCKETMODE>("SOCKETMODE");

    m_thread = thread;
    m_tcpobject = CreatTcpObject(address, port);
    m_tcpobject->moveToThread(m_thread);
    m_thread->start();

    connect(this, SIGNAL(sig_createSocket(qintptr)), m_tcpobject, SLOT(slot_createSocket(qintptr)));
    connect(this, SIGNAL(sig_createSocket(QHostAddress,quint16)),\
            m_tcpobject, SLOT(slot_createSocket(QHostAddress,quint16)));
    connect(this, SIGNAL(sig_sendData(QByteArray)), m_tcpobject, SLOT(slot_tcpWrite(QByteArray)));

    if(handle != NULL) emit sig_createSocket(handle);
    else if(handle == NULL) emit sig_createSocket(address, port);
}

TcpSocketMannager::~TcpSocketMannager()
{
    delete m_tcpobject;

    if(m_thread->isRunning())
    {
        m_thread->terminate();
        m_thread->wait();
    }
    g_.threadPool.ThreadPool_recycleThread(m_thread);
    m_thread = NULL;
}

///**——————————————————————————————————通信处理类——————————————————————————————————————**//

void BaseCommModule::setAddress(QString ip, quint16 port)
{
    mIP = ip;
    mPort = port;
}

void BaseCommModule::unpackMassage(char *msg, uint size)
{

}

void BaseCommModule::unpackMassage(QByteArray msg)
{

}

///**——————————————————————————————————通信处理子类——————————————————————————————————————**///

///**——————————————————————————————————DEMO——————————————————————————————————————**///
#include "threat_evaluator.h"

extern RadarParameters g_radar_params;
extern std::atomic<bool>  paraFlag;

void CustomCommModule::unpackMassage(char *msg, uint size)
{



    FrameHead *head = (FrameHead *)msg;

    if(head->head != 0x5a5a5a5a)
    {
        qDebug()<<"tou:"<<head->head;
        return;
    }
    if(head->len != size - sizeof(FrameHead) || head->len != sizeof(RadarParameters) + sizeof(FrameHead))
    {
        qDebug()<<"changdu:"<<head->len;
        return;
    }


    while(paraFlag)
    {
        QThread::msleep(1);
    }
    paraFlag = 1;
    memcpy(&g_radar_params, msg, sizeof(RadarParameters));
    qDebug()<<"frequency"<<g_radar_params.frequency_mhz;
    paraFlag = 0;
}

void CustomCommModule::unpackMassage(QByteArray msg)
{
    qDebug()<<"receive"<<msg.size();
    m_recvMsg0.append(msg);
    FrameHead *head = (FrameHead *)m_recvMsg0.data();
    while(m_recvMsg0.size()>sizeof(head->head)){

        if (head->head!= 0x5a5a5a5a)
         {
             m_recvMsg0.remove(0,1);
             continue;
         }

        int total_size=sizeof (FrameHead)+head->len;
        if(m_recvMsg0.size()<total_size){
            return;
        }

        FrameHead *head = (FrameHead *)m_recvMsg0.data();

        if(head->head != 0x5a5a5a5a)
        {
            return;
        }

        if(head->len != sizeof(RadarParameters) )
        {
            return;
        }

//        while(paraFlag)
//        {
//            QThread::msleep(1);
//        }
        paraFlag = 1;
        memcpy(&g_radar_params, m_recvMsg0.data() + sizeof(FrameHead) , sizeof(RadarParameters));
        qDebug()<<"frequency"<<g_radar_params.frequency_mhz;
        paraFlag = 0;

    //   emit g_.sig_run();

        m_recvMsg0.remove(0,total_size);
    }


}

///**——————————————————————————————————通信处理模块工厂 custom——————————————————————————————————————**///

// 加入自定义处理模块文件

// 自定义工厂内容
BaseCommModule *CreatUnpuckModule(QHostAddress address, quint16 port)
{
    QString ip = address.toString();

    return new CustomCommModule;
}
