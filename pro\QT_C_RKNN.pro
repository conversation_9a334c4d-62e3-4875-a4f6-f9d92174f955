#-------------------------------------------------
#
# Project created by QtCreator 2025-07-30T17:33:55
#
#-------------------------------------------------

QT       += core gui

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = QT_C_RKNN
TEMPLATE = app

DEFINES += QT_DEPRECATED_WARNINGS

# 切换到真实RKNN库时取消注释以下两行
DEFINES += HAS_RKNN=1

INCLUDEPATH += ../inc\
               /usr/lib/gcc/aarch64-linux-gnu/9/include
DESTDIR +=../bin

SOURCES += \
        ../src/*.cpp \
        ../src/*.c\
        #../lib/*.c # 使用真实RKNN时注释



HEADERS += \
        ../inc/*.h

FORMS += \
        ../ui/*.ui

include("../workmodel/workmodel.pri")

unix:!macx: LIBS += -L$$PWD/../lib/ -lrknnrt

INCLUDEPATH += $$PWD/../
DEPENDPATH += $$PWD/../
