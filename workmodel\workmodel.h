/*******************************************************************************************
 * FileProperties: 
 *     FileName: workmodel.h
 *     SvnProperties: 
 *         $URL: http://svn.hq.org/svn/HQP2334/DEVELOP/P2/5_Software/51_Host/branches/stgyModule/workmodel/workmodel.h $
 *         $Author: huangpeixuan $
 *         $Revision: 1718 $
 *         $Date: 2025-07-16 19:00:20 $
*******************************************************************************************/

/*  结构示意&&创建自定义网络通信处理(初次使用 建议阅读)
 ——————————————————————————————————————————————————————————————————————————————————————————
    一. 结构(包含)

    Workmodel [工作模块]——————API: Net ...
         |
         |—————TcpSocketMannager [通信线程与事件管理类]
         |              |
         |              |—————QThread
      ... ...           |
                        |—————TcpObject [通信管理]
                        |         |
                        |         |——————QTcpSocket
                     ... ...      |
                                  |——————BaseCommModule [通信处理模块]
                                  |
                                  |
                               ... ...

    目的：在复数通信连接/多种不同应用层数据处理 时, 将各连接运行在不同线程/模块化不同处理, 统一管理所有通信.

 ——————————————————————————————————————————————————————————————————————————————————————————
    二. 创建自定义网络通信处理

    创建一个 Workmodel 对象来管理所有通信.

    使用 Workmodel 的 API(Net_tcpclient_init\Net_tcpserver_init) 来创建或销毁 通信对象(TcpSocketMannager).

    通过派生 BaseCommModule 来自定义通信处理,
    将派生类 CustomCommModule 装载到 BaseCommModule *CreatUnpuckModule(...)接口中 (接口定义在源文件尾部),
    通过自定义该接口的处理, 创建对应 CustomCommModule 对象传给 TcpObject 使用.

    通信发送:
    1. Workmodel 的 Net_tcpWrite_ 接口可以按 ip-port 来索引通信对象进行发送.
    2. CustomCommModule 的 sig_datasend(...) 信号会触发所在 TcpObject 的发送槽函数.

    通信接收:
    派生 BaseCommModule 并重写 unpackMassage(...) ,该函数进行数据接收后的处理.

 ————————————————————————————————————————————————————————————————————————————————————————————
    主要操作步骤示意：
    1. 在自建文件(.h/.cpp)中创建 class CustomCommModule : public BaseCommModule , 重写它的 unpackMassage(...) 接口.
    2. 自定义 BaseCommModule *CreatUnpuckModule(...) 的函数定义(是否根据 ip/port 创建不同种通信处理模块 CustomCommModule ).
    3. 在契合工程结构处创建一个 Workmodel 对象.
    4. 使用 Workmodel对象, 调用接口创建或销毁通信对象.
    5. 开发过程中对 CustomCommModule::unpackMassage(...) 函数定义进行进一步修改(用于处理接收数据).
    6. 在需要发送数据处按需求调用 Workmodel::Net_tcpWrite_ 或 emit CustomCommModule::sig_datasend(...).

    ps：
    1. 有难以传递的信号时(如：从 CustomCommModule 中处理数据 将结果传给某 ui) 可以使用 globalMannager 对象 g_ 进行传递(全局变量集合 辅助管理全局变量).
    2. globalMannager::sig_commState(...) 信号会自动将当前连接状态广播, 有需求则自定义槽函数连接并处理.
*/

#ifndef WORKMODEL_H
#define WORKMODEL_H

#include "qobject.h"
#include "qwidget.h"
#include "qsplitter.h"
#include "qpushbutton.h"
#include "qspinbox.h"
#include "qcombobox.h"
#include "qstandarditemmodel.h"
#include "qlineedit.h"
#include "qcheckbox.h"
#include "qpushbutton.h"
#include "qsettings.h"
#include "qmessagebox.h"
#include "qprocess.h"
#include "qfile.h"
#include "qstring.h"
#include "qtcpserver.h"
#include "qtcpsocket.h"
#include "qudpsocket.h"
#include "qthread.h"
#include "qmap.h"
#include "qmetatype.h"
#include "qvector3d.h"
#include "qelapsedtimer.h"
#include "qreadwritelock.h"
#include "qatomic.h"
#include "assert.h"

#include "workmodelrely.h"

#include <atomic>

enum ERRORET
{
    SUCCESS = 0,
    FAILED,

    FILE_NOTEXISTS = 10,
    FILE_OPENFAIL,

    COMM_NET_ISLISTENING = 20,
    COMM_NET_LISTEN_FAILED,
    COMM_NET_NUEXISTCONNECT,
    COMM_NET_ALREADYCONNECT,
    COMM_NET_ALREADYDISCONNECT,

    COMM_NET_DISCONN_C,
    COMM_NET_DISCONN_S,
    COMM_NET_CONNTOS_FAILED,
    COMM_NET_CONNTOS_SUCCESS,
    COMM_NET_CONNTOC_SUCCESS,
};

enum SOCKETMODE
{
    SOCKET_S = 0,
    SOCKET_C = 1,
};

enum COMMTRUNK
{
    C_WHITE,
    C_REDCOMP,
};

///**—————————————————————————————————线程池管理类———————————————————————————————————————**///

class MythreadPool
{
private:
    QList<QThread *> wm_threadPool;
    QList<QThread *> wm_threadPool_free;

public:
    /**
     * @brief ThreadPool_init ///<初始化线程池
     * @param num 分配线程数
     * @return
     */
    ERRORET ThreadPool_init(quint64 num = 4);

    /**
     * @brief ThreadPool_TakeThread ///<线程池分配（分配后需start）
     * @return
     */
    QThread *ThreadPool_TakeThread();

    /**
     * @brief ThreadPool_recycleThread ///<线程回收（回收前需terminate）
     * @return
     */
    ERRORET ThreadPool_recycleThread(QThread *thread);

    /**
     * @brief ThreadPool_delete ///<释放线程池
     * @return
     */
    ERRORET ThreadPool_delete();
};

///**——————————————————————————————————数据管理类（环形链表）——————————————————————————————————————**///


template <class T>
class DNodeModel{
    typedef T data_type;

    T data;

    DNodeModel *next;
    DNodeModel *prev;
};

template <class T>
class CircleList{
    DNodeModel<T> *pHead;
    DNodeModel<T> *pTail;

    uint Len;

public:
    CircleList(uint t_NodeNum = 10){
        pHead = createNode(t_NodeNum);
        pTail->next = pHead;
        pHead->prev = pTail;
    }

    ~CircleList(){
        deleteNode(pHead);
    }

private:
    DNodeModel<T>* createNode(uint size){
        if(size == 0)
        {
            pTail = new DNodeModel<T>();
            return pTail;
        }

        DNodeModel<T> *t_pNode = new DNodeModel<T>();
        t_pNode->next = createNode(--size);
        t_pNode->next->prev = t_pNode;

        return t_pNode;
    }

    void deleteNode(DNodeModel<T> *p){
         if(p == pTail)
         {
             delete p;
             p = NULL;
             return;
         }

         deleteNode(p->next);

         delete p;
         p = NULL;
    }
};

#define MAX_BUFFER_SIZE 2048
struct dataNode
{
    char DATA[MAX_BUFFER_SIZE];

    uint size;
    dataNode *next;
};

class dataMannager
{
public:
    dataMannager(uint t_NodeNum = 12): NodeNum(t_NodeNum) {
        pHead = new dataNode();
        pHead->next = createNode(NodeNum);
        pRecv = pHead;
        pSend = pHead;
    }

    ~dataMannager(){
        deleteNode(pHead);
    }

    void recvData(char *data, uint size){
        static std::atomic<bool> sem ;
        sem.store(true);

        while(!sem){
            qDebug()<<"wait";
        }
        sem = false;

        size = size > MAX_BUFFER_SIZE? MAX_BUFFER_SIZE : size;
        memcpy(pRecv->DATA, data, size);
        pRecv->size = size;
        pRecv = pRecv->next;

        sem = true;
    }

    void recvData(QByteArray &data){
        recvLock.lockForWrite();

        pRecv->size = data.size();
        pRecv->size = pRecv->size > MAX_BUFFER_SIZE? MAX_BUFFER_SIZE : pRecv->size;
        memcpy(pRecv->DATA, data.data(), pRecv->size);
        pRecv = pRecv->next;

        recvLock.unlock();
    }

    void getSendData(QByteArray &data){
        pSend = pRecv;
        sendData(data, pHead);
    }

    uint getSendData(char* &data){
        pSend = pRecv;
        uint t_size = 0;
        sendData(data, pHead, t_size);
    }


private:
    dataNode *pHead, *pSend, *pRecv;
    uint NodeNum;

    QReadWriteLock recvLock;

    dataNode* createNode(uint size){
        if(size == 0) return pHead;

        dataNode *t_dataNode = new dataNode();
        t_dataNode->next = createNode(--size);

        return t_dataNode;
    }

    void deleteNode(dataNode *p){
         if(p == pHead)
         {
             delete p;
             p = NULL;
             return;
         }

         deleteNode(p->next);

         delete p;
         p = NULL;
    }

    void sendData(QByteArray &data, dataNode *p){
        if(p == pSend)
        {
            pHead = pSend;
            return;
        }

        data.append(p->DATA, p->size);
        sendData(data, p->next);
    }

    void sendData(char *data, dataNode *p, uint &size){
        if(p == pSend)
        {
            pHead = pSend;
            return;
        }
        size += p->size;
        memcpy(data, p->DATA, p->size);
        sendData(data + p->size, p->next, size);
    }
};


///**—————————————————————————————————全局变量管理类———————————————————————————————————————**///

class globalMannager : public QObject
{
    Q_OBJECT
public:
    MythreadPool threadPool;

    QElapsedTimer ntimer;

    QByteArray DATA;
    QByteArray DATA1;

    //custom
    bool timeFlag = false;      // 发送拷贝标志
    uint semDATA = 0;           // 记录数据接收的信号量

    uint membernum = 0;         // 接入数量

    dataMannager dataM;

signals:
    void sig_datasend_s();

    void sig_datasend_c();

    void sig_shownum(uint num); // 接入数量 信号

    void sig_commState(QString address, quint16 port, int state);

    //custom
    void sig_datasend_s(char *data, uint size);

    void sig_datasend_s(QByteArray data);

    void sig_datasend_c(char *data, uint size);

    void sig_datasend_c(QByteArray data);

    void sig_showInfo(QByteArray info, QString ip);

    void sig_showMsg(QString text, int state);

    void sig_corePara(int tcurrT, int tcurrTime, int tcurrLoop);

    void sig_run();

public slots:
    void slot_commMsg(QString address,quint16 port,int state)
    {
        qDebug()<<address<<port<<state;
    }
};

extern globalMannager g_;

///**——————————————————————————————————通信处理类——————————————————————————————————————**///

class BaseCommModule : public QObject
{
    Q_OBJECT

protected:
    QString mIP;
    quint16 mPort;

    QByteArray m_recvMsg0;
    bool m_partflag = false;                //判断是否分包

    QString name;

public:
    BaseCommModule(){}

    virtual void setAddress(QString ip, quint16 port = 815);

    virtual void unpackMassage(char *msg, uint size);

    virtual void unpackMassage(QByteArray msg);

private:

signals:
    void sig_datasend(char *data, uint size);

    void sig_powerCtrl(uint flag);

    void sig_powerCtrl(uint flag, uint swID);

public slots:
    virtual void slot_powerCtrl(uint flag){}

};

///**——————————————————————————————————通信处理子类——————————————————————————————————————**///

class CustomCommModule : public BaseCommModule
{
    Q_OBJECT

public:
    CustomCommModule(){}

    virtual void unpackMassage(char *msg, uint size);

    virtual void unpackMassage(QByteArray msg);
    void SocketData(QByteArray a_msg);
private:

};

BaseCommModule *CreatUnpuckModule(QHostAddress address = QHostAddress::Any, quint16 port = 0);

///**———————————————————————————————————通信与套接字管理类—————————————————————————————————————**///

class TcpObject : public QObject
{
    Q_OBJECT
public:
    TcpObject();
    ~TcpObject();

    QHostAddress retPeerAddress();

    QPair<QString, quint16> retPeerMsg();

protected:
    std::atomic<bool> m_flagDel{false};
    QTcpSocket *m_tcpsocket = NULL;
    QHostAddress m_peerAddr = QHostAddress("0.0.0.0");
    quint16 m_peerPort = 0;
    SOCKETMODE m_type;

    BaseCommModule *m_CommModule = NULL;

public slots:
    virtual void slot_createSocket(qintptr handle);

    virtual void slot_createSocket(const QHostAddress &address, quint16 port);

    virtual void slot_disconnect();

    virtual void slot_tcpRecv();

    virtual void slot_tcpWrite();

    virtual void slot_tcpWrite(char *data, uint size);

    virtual void slot_tcpWrite(QByteArray sendData);

    void slot_delSelf();

signals:
    void sig_delSelf();

    void sig_disconnect(QHostAddress, quint16, SOCKETMODE);

    void sig_connectFailed(QHostAddress, quint16, SOCKETMODE);
};

///**——————————————————————————————————通信与套接字管理子类——————————————————————————————————————**///



///**——————————————————————————————————服务器派生类——————————————————————————————————————**///

TcpObject *CreatTcpObject(QHostAddress address = QHostAddress::Any, quint16 port = 0);

// 服务器派生类，用于重写incomingConnection()传出套接字（使套接字在子线程生成）
class MyTcpServer : public QTcpServer
{
    Q_OBJECT
public:
    MyTcpServer(){}

protected:
    virtual void incomingConnection(qintptr handle)
    {
        emit sig_sendsocket(handle);
    }

signals:
    void sig_sendsocket(qintptr handle);
};

///**——————————————————————————————————通信线程与事件管理类——————————————————————————————————————**///

class TcpSocketMannager : public QObject
{
    Q_OBJECT
public:
    TcpSocketMannager(qintptr handle, QThread *thread,\
                      const QHostAddress &address = QHostAddress::Any, quint16 port = 0);
    ~TcpSocketMannager();

private:
    friend class Workmodel;

    TcpObject *m_tcpobject;
    QThread *m_thread;

signals:
    void sig_createSocket(qintptr handle);

    void sig_createSocket(const QHostAddress &address, quint16 port);

    void sig_sendData(QByteArray sendData);
};

///**——————————————————————————————————工作模块——————————————————————————————————————**///

class Workmodel : public QObject
{
    Q_OBJECT
private:

    QMap<QString, QProcess *> wm_processMap;

    MyTcpServer *wm_tcpserver;
    QMap<QPair<QString, quint16>, TcpSocketMannager *> wm_tcpsocketMap;
    QMap<QPair<QString, quint16>, TcpSocketMannager *> wm_tcpsocketMap_c;

    QUdpSocket *wm_udpsocket;

public:
    Workmodel();

    ~Workmodel();

    /**
     * @brief Process_run ///<运行外部进程（运行前会关闭正在运行的目标进程）
     * @param exename 进程可执行文件路径
     * @return
     */
    ERRORET Process_run(QString exename);

    /**
     * @brief Process_run ///<关闭外部进程
     * @param exename 进程可执行文件路径
     * @return
     */
    ERRORET Process_close(QString exename);

    /**
     * @brief Net_tcpserver_init ///<初始化服务端
     * @param address 本地ip地址
     * @param port 监听端口号
     * @return
     */
    ERRORET Net_tcpserver_init(const QHostAddress &address = QHostAddress::Any, quint16 port = 0);

    /**
     * @brief Net_tcpserver_close ///<关闭服务端
     * @param NULL
     * @return
     */
    ERRORET Net_tcpserver_close();

    /**
     * @brief Net_tcpclient_init ///<初始化客户端
     * @param address 目的ip地址
     * @param port 目的端口号
     * @return
     */
    ERRORET Net_tcpclient_init(const QHostAddress &address = QHostAddress::Any, quint16 port = 0);

    /**
     * @brief Net_tcpclient_close ///<关闭客户端
     * @param NULL
     * @return
     */
    ERRORET Net_tcpclient_close();

    /**
     * @brief Net_tcpclient_close ///<关闭客户端
     * @param NULL
     * @return
     */
    ERRORET Net_tcpclient_close(const QHostAddress &address, quint16 port);

    /**
     * @brief Net_tcpWrite_s2c ///<服务端写数据到客户端
     * @param address 套接字索引
     * @param sendData 发送的数据
     * @return
     */
    ERRORET Net_tcpWrite_s2c(QString address, quint16 port, QByteArray &sendData);

    /**
     * @brief Net_tcpWrite_c2s ///<客户端写数据到服务端
     * @param address 套接字索引
     * @param sendData 发送的数据
     * @return
     */
    ERRORET Net_tcpWrite_c2s(QString address, quint16 port, QByteArray &sendData);

    /**
     * @brief Net_Host_WakeUp ///<<网络唤醒
     * @param MacAddress 唤醒对象mac地址（使用前打开对象的被唤醒功能）
     * @return
     */
    ERRORET Net_Host_WakeUp(QString MacAddress);

public slots:

    ERRORET slot_tcp_newconnect(qintptr handle);

    ERRORET slot_tcp_disconnect(QHostAddress peerAddr, quint16 port, SOCKETMODE type);

    //custom

    void slot_tcpserver_init(const QHostAddress &address, quint16 port, ERRORET *ret){
        *ret = Net_tcpserver_init(address, port);
    }

    void slot_tcpserver_close(ERRORET *ret){
        *ret = Net_tcpserver_close();
    }

    void slot_tcpclient_init(const QHostAddress &address, quint16 port, ERRORET *ret){
        *ret = Net_tcpclient_init(address, port);
    }

    void slot_tcpclient_close(const QHostAddress &address, quint16 port, ERRORET *ret){
        *ret = Net_tcpclient_close(address, port);
    }

    void slot_tcpclient_closeAll(ERRORET *ret){
        *ret = Net_tcpclient_close();
    }

    void slot_tcpWrite_s2c(QString address, quint16 port, QByteArray sendData, ERRORET *ret){
        *ret = Net_tcpWrite_s2c(address, port, sendData);
    }

    void slot_tcpWrite_c2s(QString address, quint16 port, QByteArray sendData, ERRORET *ret){
        *ret = Net_tcpWrite_c2s(address, port, sendData);
    }

    void slot_Host_WakeUp(QString MacAddress, ERRORET *ret){
        *ret = Net_Host_WakeUp(MacAddress);
    }

    void slot_Host_Ping(QString address, ERRORET *ret){
        QString cmdPing = QString("ping %1 -n 1 -w %2").arg(address).arg(1000);

        QProcess cmd;
        cmd.start(cmdPing);
        cmd.waitForReadyRead(1000);
        cmd.waitForFinished(1000);

        QString res = QString::fromLocal8Bit(cmd.readAll());

        *ret = res.indexOf("TTL") == -1 ? FAILED : SUCCESS;
    }
};

/**
 * @brief Ini_ui_state_save ///<ui控件当前状态保存ini配置文件
 * @param father 待保存的父区域
 * @return
 */
ERRORET Ini_ui_state_save(QObject *father);

/**
 * @brief Ini_ui_state_load ///<ui控件当前状态加载ini配置文件
 * @param father 待加载的父区域
 * @return
 */
ERRORET Ini_ui_state_load(QObject *father);

/**
 * @brief Ini_ui_state_save_splitter ///<ui控件splitter当前状态保存ini配置文件
 * @param father 待加载的父区域
 * @return
 */
ERRORET Ini_ui_state_save_splitter(QSplitter *father);

/**
 * @brief Ini_ui_state_load_splitter ///<ui控件splitter当前状态加载ini配置文件
 * @param father 待加载的父区域
 * @return
 */
ERRORET Ini_ui_state_load_splitter(QSplitter *father);

/**
 * @brief File_copy_cover ///<文件覆盖式复制
 * @param filepath_src 源文件路径
 * @param filepath_new 目标文件路径
 * @return
 */
ERRORET File_copy_cover(QString filepath_src, QString filepath_new);

/**
 * @brief File_read_csv ///<读取csv文件并输出二维字符串列表
 * @param filepath csv文件路径
 * @param fdata 二维字符串列表
 * @return
 */
ERRORET File_read_csv(QString filepath, QList<QStringList> &fdata);

/**
 * @brief Ui_text_alignment ///<QComboBox控件复选框及下拉列表文本对齐方式
 * @param cbb QComboBox控件
 * @param alignment 文本对齐方式
 * @return
 */
ERRORET Ui_text_alignment(QComboBox *cbb, Qt::Alignment alignment);

/**
 * @brief Ui_text_alignment ///<某区域中含文本显示控件对齐控制
 * @param father 待对齐的父区域
 * @param alignment 文本对齐方式
 * @return
 */
ERRORET Ui_text_alignment(QObject *father, Qt::Alignment alignment);

/**
 * @brief Ui_dialog_show ///<生成临时提示弹窗（无自定义交互）
 * @param title 标题
 * @param text 文本
 * @param styleSheet 样式
 * @param icon 图标样式
 * @return
 */
ERRORET Ui_tips_show(QString title, QString text, QString styleSheet, QMessageBox::Icon icon);

///**————————————————————————————————————————————————————————————————————————**///

#endif // WORKMODEL_H
