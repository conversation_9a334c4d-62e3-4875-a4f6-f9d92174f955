#include "rknn_inference.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

#if HAS_RKNN
#include "rknn_api.h"
#endif

// RKNN推理实现结构体
typedef struct RKNNInferenceImpl {
    int initialized;           // 初始化标志
    char model_path[512];      // 模型文件路径
    int input_size;            // 输入维度
    int output_size;           // 输出维度
#if HAS_RKNN
    rknn_context ctx;          // RKNN上下文
    rknn_input_output_num io_num;  // 输入输出数量
    rknn_tensor_attr* input_attrs;  // 输入属性
    rknn_tensor_attr* output_attrs; // 输出属性
    unsigned char* model_data;      // 模型数据
    size_t model_size;             // 模型大小
#else
    float* weights;            // 模拟权重（无RKNN时）
#endif
} RKNNInferenceImpl;

/* 加载模型文件 */
static unsigned char* load_model_file(const char* model_path, size_t* model_size) {
    FILE* file = fopen(model_path, "rb");
    if (!file) {
        // 无法打开模型文件
        return NULL;
    }

    fseek(file, 0, SEEK_END);
    *model_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    unsigned char* model_data = (unsigned char*)malloc(*model_size);
    if (!model_data) {
        // 无法分配模型内存
        fclose(file);
        return NULL;
    }

    size_t read_size = fread(model_data, 1, *model_size, file);
    fclose(file);

    if (read_size != *model_size) {
        // 模型文件读取不完整
        free(model_data);
        return NULL;
    }

    return model_data;
}

/* 创建RKNN推理引擎 */
RKNNInference* rknn_inference_create(const char* model_path) {
    if (!model_path) {
        // RKNN模型路径为空
        return NULL;
    }
    
    RKNNInferenceImpl* impl = (RKNNInferenceImpl*)malloc(sizeof(RKNNInferenceImpl));
    if (!impl) {
        // 无法分配内存
        return NULL;
    }
    
    memset(impl, 0, sizeof(RKNNInferenceImpl));
    strcpy(impl->model_path, model_path);
    
    impl->input_size = 12;
    impl->output_size = 32;

#if HAS_RKNN
    /* 加载RKNN模型文件 */
    impl->model_data = load_model_file(model_path, &impl->model_size);
    if (!impl->model_data) {
        printf("Error: Failed to load RKNN model file\n");
        free(impl);
        return NULL;
    }

    /* 初始化RKNN上下文 */
    int ret = rknn_init(&impl->ctx, impl->model_data, impl->model_size, 0, NULL);
    if (ret != RKNN_SUCC) {
        printf("Error: RKNN init failed, ret=%d\n", ret);
        free(impl->model_data);
        free(impl);
        return NULL;
    }

    /* 查询输入输出数量 */
    ret = rknn_query(impl->ctx, RKNN_QUERY_IN_OUT_NUM, &impl->io_num, sizeof(impl->io_num));
    if (ret != RKNN_SUCC) {
        printf("Error: RKNN query io_num failed, ret=%d\n", ret);
        rknn_destroy(impl->ctx);
        free(impl->model_data);
        free(impl);
        return NULL;
    }

    // RKNN模型信息已加载

    /* 查询输入属性 */
    impl->input_attrs = (rknn_tensor_attr*)malloc(impl->io_num.n_input * sizeof(rknn_tensor_attr));
    for (uint32_t i = 0; i < impl->io_num.n_input; i++) {
        impl->input_attrs[i].index = i;
        ret = rknn_query(impl->ctx, RKNN_QUERY_INPUT_ATTR, &impl->input_attrs[i], sizeof(rknn_tensor_attr));
        if (ret != RKNN_SUCC) {
            printf("Error: RKNN query input attr %d failed, ret=%d\n", i, ret);
            free(impl->input_attrs);
            rknn_destroy(impl->ctx);
            free(impl->model_data);
            free(impl);
            return NULL;
        }
        // 输入属性已查询
    }

    /* 查询输出属性 */
    impl->output_attrs = (rknn_tensor_attr*)malloc(impl->io_num.n_output * sizeof(rknn_tensor_attr));
    for (uint32_t i = 0; i < impl->io_num.n_output; i++) {
        impl->output_attrs[i].index = i;
        ret = rknn_query(impl->ctx, RKNN_QUERY_OUTPUT_ATTR, &impl->output_attrs[i], sizeof(rknn_tensor_attr));
        if (ret != RKNN_SUCC) {
            printf("Error: RKNN query output attr %d failed, ret=%d\n", i, ret);
            free(impl->output_attrs);
            free(impl->input_attrs);
            rknn_destroy(impl->ctx);
            free(impl->model_data);
            free(impl);
            return NULL;
        }
        // 输出属性已查询
    }
#else
    impl->weights = (float*)malloc(impl->output_size * impl->input_size * sizeof(float));
    if (!impl->weights) {
        printf("Error: Cannot allocate weights memory\n");
        free(impl);
        return NULL;
    }

    srand(12345);
    for (int i = 0; i < impl->output_size * impl->input_size; i++) {
        impl->weights[i] = ((float)rand() / RAND_MAX - 0.5f) * 2.0f;
    }
#endif
    
    impl->initialized = 1;
    
    // RKNN推理引擎初始化成功
    
    return (RKNNInference*)impl;
}

/* 销毁RKNN推理引擎 */
void rknn_inference_destroy(RKNNInference* inference) {
    if (!inference) {
        return;
    }
    
    RKNNInferenceImpl* impl = (RKNNInferenceImpl*)inference;

#if HAS_RKNN
    if (impl->output_attrs) {
        free(impl->output_attrs);
    }
    if (impl->input_attrs) {
        free(impl->input_attrs);
    }
    if (impl->ctx) {
        rknn_destroy(impl->ctx);
    }
    if (impl->model_data) {
        free(impl->model_data);
    }
#else
    if (impl->weights) {
        free(impl->weights);
    }
#endif

    free(impl);
    // RKNN推理引擎已销毁
}

/* 验证输入数据 */
 int rknn_validate_input(const RKNNInput* input) {
    if (!input) {
        return 0;
    }
    
    if (input->input_size != 12) {
        printf("Input dimension error: expected 12, got %d\n", input->input_size);
        return 0;
    }
    
    return 1;
}

/* 执行RKNN推理 */
int rknn_inference_predict(RKNNInference* inference, 
                          const RKNNInput* input, 
                          RKNNOutput* output) {
    if (!inference || !input || !output) {
        printf("Error: Inference parameters are null\n");
        return -1;
    }
    
    RKNNInferenceImpl* impl = (RKNNInferenceImpl*)inference;
    if (!impl->initialized) {
        printf("Error: RKNN inference engine not initialized\n");
        return -2;
    }
    
    if (!rknn_validate_input(input)) {
        printf("Error: Input data validation failed\n");
        return -3;
    }

    output->output_size = impl->output_size;
    output->output_data = (float*)malloc(output->output_size * sizeof(float));
    if (!output->output_data) {
        return -7;
    }
    
#if HAS_RKNN
    /* 真实RKNN推理 */
    rknn_input rknn_inputs[1];
    memset(rknn_inputs, 0, sizeof(rknn_inputs));

    /* 设置输入数据 */
    rknn_inputs[0].index = 0;
    rknn_inputs[0].type = RKNN_TENSOR_FLOAT32;
    rknn_inputs[0].fmt = RKNN_TENSOR_NCHW;
    rknn_inputs[0].size = input->input_size * sizeof(float);
    rknn_inputs[0].buf = input->input_data;

    int ret = rknn_inputs_set(impl->ctx, impl->io_num.n_input, rknn_inputs);
    if (ret != RKNN_SUCC) {
        printf("Error: RKNN inputs set failed, ret=%d\n", ret);
        free(output->output_data);
        output->output_data = NULL;
        return -4;
    }

    /* 运行推理 */
    ret = rknn_run(impl->ctx, NULL);
    if (ret != RKNN_SUCC) {
        printf("Error: RKNN run failed, ret=%d\n", ret);
        free(output->output_data);
        output->output_data = NULL;
        return -5;
    }

    /* 获取输出 */
    rknn_output* rknn_outputs = (rknn_output*)malloc(impl->io_num.n_output * sizeof(rknn_output));
    if (!rknn_outputs) {
        printf("Error: Cannot allocate memory for outputs\n");
        free(output->output_data);
        output->output_data = NULL;
        return -8;
    }
    memset(rknn_outputs, 0, impl->io_num.n_output * sizeof(rknn_output));

    for (uint32_t i = 0; i < impl->io_num.n_output; i++) {
        rknn_outputs[i].index = i;
        rknn_outputs[i].want_float = 1;
    }

    ret = rknn_outputs_get(impl->ctx, impl->io_num.n_output, rknn_outputs, NULL);
    if (ret != RKNN_SUCC) {
        printf("Error: RKNN outputs get failed, ret=%d\n", ret);
        free(output->output_data);
        output->output_data = NULL;
        return -6;
    }

    /* 复制输出数据 */
    int total_output_count = 0;
    for (uint32_t i = 0; i < impl->io_num.n_output && total_output_count < output->output_size; i++) {
        float* output_data_ptr = (float*)rknn_outputs[i].buf;
        int output_count = rknn_outputs[i].size / sizeof(float);

        for (int j = 0; j < output_count && total_output_count < output->output_size; j++) {
            output->output_data[total_output_count++] = output_data_ptr[j];
        }
    }

    /* 更新实际输出大小 */
    output->output_size = total_output_count;

    /* 释放输出 */
    rknn_outputs_release(impl->ctx, impl->io_num.n_output, rknn_outputs);

#else
    /* 模拟推理 */
    for (int i = 0; i < impl->output_size; i++) {
        float sum = 0.0f;
        for (int j = 0; j < input->input_size; j++) {
            sum += input->input_data[j] * impl->weights[i * input->input_size + j];
        }
        output->output_data[i] = 1.0f / (1.0f + expf(-sum));
    }
#endif
    
    output->valid = 1;
    
    // RKNN推理完成
    
    return 0;
}

/* 释放输出内存 */
void rknn_free_output(RKNNOutput* output) {
    if (output && output->output_data) {
        free(output->output_data);
        output->output_data = NULL;
        output->output_size = 0;
        output->valid = 0;
    }
}
