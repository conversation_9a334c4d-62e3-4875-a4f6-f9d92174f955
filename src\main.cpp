﻿#include "mainwindow.h"
#include <QApplication>
#include <QThread>
#include "workmodel.h"
#include "qt_main_c.h"
#include "qapplication.h"
#include "qtimer.h"

extern std::atomic<bool>  paraFlag;

int main(int argc, char *argv[])
{
    int ret;
    paraFlag = 0;
    QApplication a(argc, argv);
    Workmodel wm;
    MainWindow w;
    qDebug()<<sizeof(RadarParameters);
//    QObject::connect(&g_,&globalMannager::sig_commState, &g_, &globalMannager::slot_commMsg);

//    qDebug()<<wm.Net_tcpserver_init(QHostAddress("************"), 8080);

  run_complete_test_scenario( );
    w.show();
    ret=a.exec();

//    wm.Net_tcpserver_close();
//    destroy_all_modules();

    return ret;
}
