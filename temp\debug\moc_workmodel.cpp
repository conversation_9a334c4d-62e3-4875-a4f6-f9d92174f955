/****************************************************************************
** Meta object code from reading C++ file 'workmodel.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../workmodel/workmodel.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'workmodel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_globalMannager_t {
    QByteArrayData data[24];
    char stringdata0[210];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_globalMannager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_globalMannager_t qt_meta_stringdata_globalMannager = {
    {
QT_MOC_LITERAL(0, 0, 14), // "globalMannager"
QT_MOC_LITERAL(1, 15, 14), // "sig_datasend_s"
QT_MOC_LITERAL(2, 30, 0), // ""
QT_MOC_LITERAL(3, 31, 14), // "sig_datasend_c"
QT_MOC_LITERAL(4, 46, 11), // "sig_shownum"
QT_MOC_LITERAL(5, 58, 3), // "num"
QT_MOC_LITERAL(6, 62, 13), // "sig_commState"
QT_MOC_LITERAL(7, 76, 7), // "address"
QT_MOC_LITERAL(8, 84, 4), // "port"
QT_MOC_LITERAL(9, 89, 5), // "state"
QT_MOC_LITERAL(10, 95, 5), // "char*"
QT_MOC_LITERAL(11, 101, 4), // "data"
QT_MOC_LITERAL(12, 106, 4), // "size"
QT_MOC_LITERAL(13, 111, 12), // "sig_showInfo"
QT_MOC_LITERAL(14, 124, 4), // "info"
QT_MOC_LITERAL(15, 129, 2), // "ip"
QT_MOC_LITERAL(16, 132, 11), // "sig_showMsg"
QT_MOC_LITERAL(17, 144, 4), // "text"
QT_MOC_LITERAL(18, 149, 12), // "sig_corePara"
QT_MOC_LITERAL(19, 162, 6), // "tcurrT"
QT_MOC_LITERAL(20, 169, 9), // "tcurrTime"
QT_MOC_LITERAL(21, 179, 9), // "tcurrLoop"
QT_MOC_LITERAL(22, 189, 7), // "sig_run"
QT_MOC_LITERAL(23, 197, 12) // "slot_commMsg"

    },
    "globalMannager\0sig_datasend_s\0\0"
    "sig_datasend_c\0sig_shownum\0num\0"
    "sig_commState\0address\0port\0state\0char*\0"
    "data\0size\0sig_showInfo\0info\0ip\0"
    "sig_showMsg\0text\0sig_corePara\0tcurrT\0"
    "tcurrTime\0tcurrLoop\0sig_run\0slot_commMsg"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_globalMannager[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      13,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      12,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   79,    2, 0x06 /* Public */,
       3,    0,   80,    2, 0x06 /* Public */,
       4,    1,   81,    2, 0x06 /* Public */,
       6,    3,   84,    2, 0x06 /* Public */,
       1,    2,   91,    2, 0x06 /* Public */,
       1,    1,   96,    2, 0x06 /* Public */,
       3,    2,   99,    2, 0x06 /* Public */,
       3,    1,  104,    2, 0x06 /* Public */,
      13,    2,  107,    2, 0x06 /* Public */,
      16,    2,  112,    2, 0x06 /* Public */,
      18,    3,  117,    2, 0x06 /* Public */,
      22,    0,  124,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      23,    3,  125,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::UInt,    5,
    QMetaType::Void, QMetaType::QString, QMetaType::UShort, QMetaType::Int,    7,    8,    9,
    QMetaType::Void, 0x80000000 | 10, QMetaType::UInt,   11,   12,
    QMetaType::Void, QMetaType::QByteArray,   11,
    QMetaType::Void, 0x80000000 | 10, QMetaType::UInt,   11,   12,
    QMetaType::Void, QMetaType::QByteArray,   11,
    QMetaType::Void, QMetaType::QByteArray, QMetaType::QString,   14,   15,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   17,    9,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int,   19,   20,   21,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::UShort, QMetaType::Int,    7,    8,    9,

       0        // eod
};

void globalMannager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        globalMannager *_t = static_cast<globalMannager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->sig_datasend_s(); break;
        case 1: _t->sig_datasend_c(); break;
        case 2: _t->sig_shownum((*reinterpret_cast< uint(*)>(_a[1]))); break;
        case 3: _t->sig_commState((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 4: _t->sig_datasend_s((*reinterpret_cast< char*(*)>(_a[1])),(*reinterpret_cast< uint(*)>(_a[2]))); break;
        case 5: _t->sig_datasend_s((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 6: _t->sig_datasend_c((*reinterpret_cast< char*(*)>(_a[1])),(*reinterpret_cast< uint(*)>(_a[2]))); break;
        case 7: _t->sig_datasend_c((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 8: _t->sig_showInfo((*reinterpret_cast< QByteArray(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2]))); break;
        case 9: _t->sig_showMsg((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 10: _t->sig_corePara((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 11: _t->sig_run(); break;
        case 12: _t->slot_commMsg((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (globalMannager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&globalMannager::sig_datasend_s)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (globalMannager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&globalMannager::sig_datasend_c)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (globalMannager::*_t)(uint );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&globalMannager::sig_shownum)) {
                *result = 2;
                return;
            }
        }
        {
            typedef void (globalMannager::*_t)(QString , quint16 , int );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&globalMannager::sig_commState)) {
                *result = 3;
                return;
            }
        }
        {
            typedef void (globalMannager::*_t)(char * , uint );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&globalMannager::sig_datasend_s)) {
                *result = 4;
                return;
            }
        }
        {
            typedef void (globalMannager::*_t)(QByteArray );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&globalMannager::sig_datasend_s)) {
                *result = 5;
                return;
            }
        }
        {
            typedef void (globalMannager::*_t)(char * , uint );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&globalMannager::sig_datasend_c)) {
                *result = 6;
                return;
            }
        }
        {
            typedef void (globalMannager::*_t)(QByteArray );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&globalMannager::sig_datasend_c)) {
                *result = 7;
                return;
            }
        }
        {
            typedef void (globalMannager::*_t)(QByteArray , QString );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&globalMannager::sig_showInfo)) {
                *result = 8;
                return;
            }
        }
        {
            typedef void (globalMannager::*_t)(QString , int );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&globalMannager::sig_showMsg)) {
                *result = 9;
                return;
            }
        }
        {
            typedef void (globalMannager::*_t)(int , int , int );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&globalMannager::sig_corePara)) {
                *result = 10;
                return;
            }
        }
        {
            typedef void (globalMannager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&globalMannager::sig_run)) {
                *result = 11;
                return;
            }
        }
    }
}

const QMetaObject globalMannager::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_globalMannager.data,
      qt_meta_data_globalMannager,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *globalMannager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *globalMannager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_globalMannager.stringdata0))
        return static_cast<void*>(const_cast< globalMannager*>(this));
    return QObject::qt_metacast(_clname);
}

int globalMannager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 13)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 13;
    }
    return _id;
}

// SIGNAL 0
void globalMannager::sig_datasend_s()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void globalMannager::sig_datasend_c()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void globalMannager::sig_shownum(uint _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void globalMannager::sig_commState(QString _t1, quint16 _t2, int _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)), const_cast<void*>(reinterpret_cast<const void*>(&_t3)) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void globalMannager::sig_datasend_s(char * _t1, uint _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void globalMannager::sig_datasend_s(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void globalMannager::sig_datasend_c(char * _t1, uint _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void globalMannager::sig_datasend_c(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}

// SIGNAL 8
void globalMannager::sig_showInfo(QByteArray _t1, QString _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 8, _a);
}

// SIGNAL 9
void globalMannager::sig_showMsg(QString _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 9, _a);
}

// SIGNAL 10
void globalMannager::sig_corePara(int _t1, int _t2, int _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)), const_cast<void*>(reinterpret_cast<const void*>(&_t3)) };
    QMetaObject::activate(this, &staticMetaObject, 10, _a);
}

// SIGNAL 11
void globalMannager::sig_run()
{
    QMetaObject::activate(this, &staticMetaObject, 11, nullptr);
}
struct qt_meta_stringdata_BaseCommModule_t {
    QByteArrayData data[10];
    char stringdata0[84];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_BaseCommModule_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_BaseCommModule_t qt_meta_stringdata_BaseCommModule = {
    {
QT_MOC_LITERAL(0, 0, 14), // "BaseCommModule"
QT_MOC_LITERAL(1, 15, 12), // "sig_datasend"
QT_MOC_LITERAL(2, 28, 0), // ""
QT_MOC_LITERAL(3, 29, 5), // "char*"
QT_MOC_LITERAL(4, 35, 4), // "data"
QT_MOC_LITERAL(5, 40, 4), // "size"
QT_MOC_LITERAL(6, 45, 13), // "sig_powerCtrl"
QT_MOC_LITERAL(7, 59, 4), // "flag"
QT_MOC_LITERAL(8, 64, 4), // "swID"
QT_MOC_LITERAL(9, 69, 14) // "slot_powerCtrl"

    },
    "BaseCommModule\0sig_datasend\0\0char*\0"
    "data\0size\0sig_powerCtrl\0flag\0swID\0"
    "slot_powerCtrl"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_BaseCommModule[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   34,    2, 0x06 /* Public */,
       6,    1,   39,    2, 0x06 /* Public */,
       6,    2,   42,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       9,    1,   47,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3, QMetaType::UInt,    4,    5,
    QMetaType::Void, QMetaType::UInt,    7,
    QMetaType::Void, QMetaType::UInt, QMetaType::UInt,    7,    8,

 // slots: parameters
    QMetaType::Void, QMetaType::UInt,    7,

       0        // eod
};

void BaseCommModule::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        BaseCommModule *_t = static_cast<BaseCommModule *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->sig_datasend((*reinterpret_cast< char*(*)>(_a[1])),(*reinterpret_cast< uint(*)>(_a[2]))); break;
        case 1: _t->sig_powerCtrl((*reinterpret_cast< uint(*)>(_a[1]))); break;
        case 2: _t->sig_powerCtrl((*reinterpret_cast< uint(*)>(_a[1])),(*reinterpret_cast< uint(*)>(_a[2]))); break;
        case 3: _t->slot_powerCtrl((*reinterpret_cast< uint(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (BaseCommModule::*_t)(char * , uint );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&BaseCommModule::sig_datasend)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (BaseCommModule::*_t)(uint );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&BaseCommModule::sig_powerCtrl)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (BaseCommModule::*_t)(uint , uint );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&BaseCommModule::sig_powerCtrl)) {
                *result = 2;
                return;
            }
        }
    }
}

const QMetaObject BaseCommModule::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_BaseCommModule.data,
      qt_meta_data_BaseCommModule,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *BaseCommModule::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *BaseCommModule::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_BaseCommModule.stringdata0))
        return static_cast<void*>(const_cast< BaseCommModule*>(this));
    return QObject::qt_metacast(_clname);
}

int BaseCommModule::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void BaseCommModule::sig_datasend(char * _t1, uint _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void BaseCommModule::sig_powerCtrl(uint _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void BaseCommModule::sig_powerCtrl(uint _t1, uint _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
struct qt_meta_stringdata_CustomCommModule_t {
    QByteArrayData data[1];
    char stringdata0[17];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CustomCommModule_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CustomCommModule_t qt_meta_stringdata_CustomCommModule = {
    {
QT_MOC_LITERAL(0, 0, 16) // "CustomCommModule"

    },
    "CustomCommModule"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CustomCommModule[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void CustomCommModule::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

const QMetaObject CustomCommModule::staticMetaObject = {
    { &BaseCommModule::staticMetaObject, qt_meta_stringdata_CustomCommModule.data,
      qt_meta_data_CustomCommModule,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *CustomCommModule::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CustomCommModule::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CustomCommModule.stringdata0))
        return static_cast<void*>(const_cast< CustomCommModule*>(this));
    return BaseCommModule::qt_metacast(_clname);
}

int CustomCommModule::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = BaseCommModule::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_TcpObject_t {
    QByteArrayData data[20];
    char stringdata0[207];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_TcpObject_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_TcpObject_t qt_meta_stringdata_TcpObject = {
    {
QT_MOC_LITERAL(0, 0, 9), // "TcpObject"
QT_MOC_LITERAL(1, 10, 11), // "sig_delSelf"
QT_MOC_LITERAL(2, 22, 0), // ""
QT_MOC_LITERAL(3, 23, 14), // "sig_disconnect"
QT_MOC_LITERAL(4, 38, 12), // "QHostAddress"
QT_MOC_LITERAL(5, 51, 10), // "SOCKETMODE"
QT_MOC_LITERAL(6, 62, 17), // "sig_connectFailed"
QT_MOC_LITERAL(7, 80, 17), // "slot_createSocket"
QT_MOC_LITERAL(8, 98, 7), // "qintptr"
QT_MOC_LITERAL(9, 106, 6), // "handle"
QT_MOC_LITERAL(10, 113, 7), // "address"
QT_MOC_LITERAL(11, 121, 4), // "port"
QT_MOC_LITERAL(12, 126, 15), // "slot_disconnect"
QT_MOC_LITERAL(13, 142, 12), // "slot_tcpRecv"
QT_MOC_LITERAL(14, 155, 13), // "slot_tcpWrite"
QT_MOC_LITERAL(15, 169, 5), // "char*"
QT_MOC_LITERAL(16, 175, 4), // "data"
QT_MOC_LITERAL(17, 180, 4), // "size"
QT_MOC_LITERAL(18, 185, 8), // "sendData"
QT_MOC_LITERAL(19, 194, 12) // "slot_delSelf"

    },
    "TcpObject\0sig_delSelf\0\0sig_disconnect\0"
    "QHostAddress\0SOCKETMODE\0sig_connectFailed\0"
    "slot_createSocket\0qintptr\0handle\0"
    "address\0port\0slot_disconnect\0slot_tcpRecv\0"
    "slot_tcpWrite\0char*\0data\0size\0sendData\0"
    "slot_delSelf"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_TcpObject[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      11,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   69,    2, 0x06 /* Public */,
       3,    3,   70,    2, 0x06 /* Public */,
       6,    3,   77,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       7,    1,   84,    2, 0x0a /* Public */,
       7,    2,   87,    2, 0x0a /* Public */,
      12,    0,   92,    2, 0x0a /* Public */,
      13,    0,   93,    2, 0x0a /* Public */,
      14,    0,   94,    2, 0x0a /* Public */,
      14,    2,   95,    2, 0x0a /* Public */,
      14,    1,  100,    2, 0x0a /* Public */,
      19,    0,  103,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 4, QMetaType::UShort, 0x80000000 | 5,    2,    2,    2,
    QMetaType::Void, 0x80000000 | 4, QMetaType::UShort, 0x80000000 | 5,    2,    2,    2,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 8,    9,
    QMetaType::Void, 0x80000000 | 4, QMetaType::UShort,   10,   11,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 15, QMetaType::UInt,   16,   17,
    QMetaType::Void, QMetaType::QByteArray,   18,
    QMetaType::Void,

       0        // eod
};

void TcpObject::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        TcpObject *_t = static_cast<TcpObject *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->sig_delSelf(); break;
        case 1: _t->sig_disconnect((*reinterpret_cast< QHostAddress(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2])),(*reinterpret_cast< SOCKETMODE(*)>(_a[3]))); break;
        case 2: _t->sig_connectFailed((*reinterpret_cast< QHostAddress(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2])),(*reinterpret_cast< SOCKETMODE(*)>(_a[3]))); break;
        case 3: _t->slot_createSocket((*reinterpret_cast< qintptr(*)>(_a[1]))); break;
        case 4: _t->slot_createSocket((*reinterpret_cast< const QHostAddress(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2]))); break;
        case 5: _t->slot_disconnect(); break;
        case 6: _t->slot_tcpRecv(); break;
        case 7: _t->slot_tcpWrite(); break;
        case 8: _t->slot_tcpWrite((*reinterpret_cast< char*(*)>(_a[1])),(*reinterpret_cast< uint(*)>(_a[2]))); break;
        case 9: _t->slot_tcpWrite((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        case 10: _t->slot_delSelf(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (TcpObject::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&TcpObject::sig_delSelf)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (TcpObject::*_t)(QHostAddress , quint16 , SOCKETMODE );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&TcpObject::sig_disconnect)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (TcpObject::*_t)(QHostAddress , quint16 , SOCKETMODE );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&TcpObject::sig_connectFailed)) {
                *result = 2;
                return;
            }
        }
    }
}

const QMetaObject TcpObject::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_TcpObject.data,
      qt_meta_data_TcpObject,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *TcpObject::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TcpObject::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_TcpObject.stringdata0))
        return static_cast<void*>(const_cast< TcpObject*>(this));
    return QObject::qt_metacast(_clname);
}

int TcpObject::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 11;
    }
    return _id;
}

// SIGNAL 0
void TcpObject::sig_delSelf()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void TcpObject::sig_disconnect(QHostAddress _t1, quint16 _t2, SOCKETMODE _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)), const_cast<void*>(reinterpret_cast<const void*>(&_t3)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void TcpObject::sig_connectFailed(QHostAddress _t1, quint16 _t2, SOCKETMODE _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)), const_cast<void*>(reinterpret_cast<const void*>(&_t3)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
struct qt_meta_stringdata_MyTcpServer_t {
    QByteArrayData data[5];
    char stringdata0[43];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MyTcpServer_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MyTcpServer_t qt_meta_stringdata_MyTcpServer = {
    {
QT_MOC_LITERAL(0, 0, 11), // "MyTcpServer"
QT_MOC_LITERAL(1, 12, 14), // "sig_sendsocket"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 7), // "qintptr"
QT_MOC_LITERAL(4, 36, 6) // "handle"

    },
    "MyTcpServer\0sig_sendsocket\0\0qintptr\0"
    "handle"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MyTcpServer[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       1,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   19,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,

       0        // eod
};

void MyTcpServer::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        MyTcpServer *_t = static_cast<MyTcpServer *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->sig_sendsocket((*reinterpret_cast< qintptr(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (MyTcpServer::*_t)(qintptr );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&MyTcpServer::sig_sendsocket)) {
                *result = 0;
                return;
            }
        }
    }
}

const QMetaObject MyTcpServer::staticMetaObject = {
    { &QTcpServer::staticMetaObject, qt_meta_stringdata_MyTcpServer.data,
      qt_meta_data_MyTcpServer,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *MyTcpServer::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MyTcpServer::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MyTcpServer.stringdata0))
        return static_cast<void*>(const_cast< MyTcpServer*>(this));
    return QTcpServer::qt_metacast(_clname);
}

int MyTcpServer::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QTcpServer::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 1)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 1;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 1)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 1;
    }
    return _id;
}

// SIGNAL 0
void MyTcpServer::sig_sendsocket(qintptr _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
struct qt_meta_stringdata_TcpSocketMannager_t {
    QByteArrayData data[10];
    char stringdata0[99];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_TcpSocketMannager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_TcpSocketMannager_t qt_meta_stringdata_TcpSocketMannager = {
    {
QT_MOC_LITERAL(0, 0, 17), // "TcpSocketMannager"
QT_MOC_LITERAL(1, 18, 16), // "sig_createSocket"
QT_MOC_LITERAL(2, 35, 0), // ""
QT_MOC_LITERAL(3, 36, 7), // "qintptr"
QT_MOC_LITERAL(4, 44, 6), // "handle"
QT_MOC_LITERAL(5, 51, 12), // "QHostAddress"
QT_MOC_LITERAL(6, 64, 7), // "address"
QT_MOC_LITERAL(7, 72, 4), // "port"
QT_MOC_LITERAL(8, 77, 12), // "sig_sendData"
QT_MOC_LITERAL(9, 90, 8) // "sendData"

    },
    "TcpSocketMannager\0sig_createSocket\0\0"
    "qintptr\0handle\0QHostAddress\0address\0"
    "port\0sig_sendData\0sendData"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_TcpSocketMannager[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       3,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   29,    2, 0x06 /* Public */,
       1,    2,   32,    2, 0x06 /* Public */,
       8,    1,   37,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 5, QMetaType::UShort,    6,    7,
    QMetaType::Void, QMetaType::QByteArray,    9,

       0        // eod
};

void TcpSocketMannager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        TcpSocketMannager *_t = static_cast<TcpSocketMannager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->sig_createSocket((*reinterpret_cast< qintptr(*)>(_a[1]))); break;
        case 1: _t->sig_createSocket((*reinterpret_cast< const QHostAddress(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2]))); break;
        case 2: _t->sig_sendData((*reinterpret_cast< QByteArray(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (TcpSocketMannager::*_t)(qintptr );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&TcpSocketMannager::sig_createSocket)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (TcpSocketMannager::*_t)(const QHostAddress & , quint16 );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&TcpSocketMannager::sig_createSocket)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (TcpSocketMannager::*_t)(QByteArray );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&TcpSocketMannager::sig_sendData)) {
                *result = 2;
                return;
            }
        }
    }
}

const QMetaObject TcpSocketMannager::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_TcpSocketMannager.data,
      qt_meta_data_TcpSocketMannager,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *TcpSocketMannager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TcpSocketMannager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_TcpSocketMannager.stringdata0))
        return static_cast<void*>(const_cast< TcpSocketMannager*>(this));
    return QObject::qt_metacast(_clname);
}

int TcpSocketMannager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void TcpSocketMannager::sig_createSocket(qintptr _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void TcpSocketMannager::sig_createSocket(const QHostAddress & _t1, quint16 _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void TcpSocketMannager::sig_sendData(QByteArray _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
struct qt_meta_stringdata_Workmodel_t {
    QByteArrayData data[26];
    char stringdata0[332];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_Workmodel_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_Workmodel_t qt_meta_stringdata_Workmodel = {
    {
QT_MOC_LITERAL(0, 0, 9), // "Workmodel"
QT_MOC_LITERAL(1, 10, 19), // "slot_tcp_newconnect"
QT_MOC_LITERAL(2, 30, 7), // "ERRORET"
QT_MOC_LITERAL(3, 38, 0), // ""
QT_MOC_LITERAL(4, 39, 7), // "qintptr"
QT_MOC_LITERAL(5, 47, 6), // "handle"
QT_MOC_LITERAL(6, 54, 19), // "slot_tcp_disconnect"
QT_MOC_LITERAL(7, 74, 12), // "QHostAddress"
QT_MOC_LITERAL(8, 87, 8), // "peerAddr"
QT_MOC_LITERAL(9, 96, 4), // "port"
QT_MOC_LITERAL(10, 101, 10), // "SOCKETMODE"
QT_MOC_LITERAL(11, 112, 4), // "type"
QT_MOC_LITERAL(12, 117, 19), // "slot_tcpserver_init"
QT_MOC_LITERAL(13, 137, 7), // "address"
QT_MOC_LITERAL(14, 145, 8), // "ERRORET*"
QT_MOC_LITERAL(15, 154, 3), // "ret"
QT_MOC_LITERAL(16, 158, 20), // "slot_tcpserver_close"
QT_MOC_LITERAL(17, 179, 19), // "slot_tcpclient_init"
QT_MOC_LITERAL(18, 199, 20), // "slot_tcpclient_close"
QT_MOC_LITERAL(19, 220, 23), // "slot_tcpclient_closeAll"
QT_MOC_LITERAL(20, 244, 17), // "slot_tcpWrite_s2c"
QT_MOC_LITERAL(21, 262, 8), // "sendData"
QT_MOC_LITERAL(22, 271, 17), // "slot_tcpWrite_c2s"
QT_MOC_LITERAL(23, 289, 16), // "slot_Host_WakeUp"
QT_MOC_LITERAL(24, 306, 10), // "MacAddress"
QT_MOC_LITERAL(25, 317, 14) // "slot_Host_Ping"

    },
    "Workmodel\0slot_tcp_newconnect\0ERRORET\0"
    "\0qintptr\0handle\0slot_tcp_disconnect\0"
    "QHostAddress\0peerAddr\0port\0SOCKETMODE\0"
    "type\0slot_tcpserver_init\0address\0"
    "ERRORET*\0ret\0slot_tcpserver_close\0"
    "slot_tcpclient_init\0slot_tcpclient_close\0"
    "slot_tcpclient_closeAll\0slot_tcpWrite_s2c\0"
    "sendData\0slot_tcpWrite_c2s\0slot_Host_WakeUp\0"
    "MacAddress\0slot_Host_Ping"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_Workmodel[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      11,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    1,   69,    3, 0x0a /* Public */,
       6,    3,   72,    3, 0x0a /* Public */,
      12,    3,   79,    3, 0x0a /* Public */,
      16,    1,   86,    3, 0x0a /* Public */,
      17,    3,   89,    3, 0x0a /* Public */,
      18,    3,   96,    3, 0x0a /* Public */,
      19,    1,  103,    3, 0x0a /* Public */,
      20,    4,  106,    3, 0x0a /* Public */,
      22,    4,  115,    3, 0x0a /* Public */,
      23,    2,  124,    3, 0x0a /* Public */,
      25,    2,  129,    3, 0x0a /* Public */,

 // slots: parameters
    0x80000000 | 2, 0x80000000 | 4,    5,
    0x80000000 | 2, 0x80000000 | 7, QMetaType::UShort, 0x80000000 | 10,    8,    9,   11,
    QMetaType::Void, 0x80000000 | 7, QMetaType::UShort, 0x80000000 | 14,   13,    9,   15,
    QMetaType::Void, 0x80000000 | 14,   15,
    QMetaType::Void, 0x80000000 | 7, QMetaType::UShort, 0x80000000 | 14,   13,    9,   15,
    QMetaType::Void, 0x80000000 | 7, QMetaType::UShort, 0x80000000 | 14,   13,    9,   15,
    QMetaType::Void, 0x80000000 | 14,   15,
    QMetaType::Void, QMetaType::QString, QMetaType::UShort, QMetaType::QByteArray, 0x80000000 | 14,   13,    9,   21,   15,
    QMetaType::Void, QMetaType::QString, QMetaType::UShort, QMetaType::QByteArray, 0x80000000 | 14,   13,    9,   21,   15,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 14,   24,   15,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 14,   13,   15,

       0        // eod
};

void Workmodel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        Workmodel *_t = static_cast<Workmodel *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: { ERRORET _r = _t->slot_tcp_newconnect((*reinterpret_cast< qintptr(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< ERRORET*>(_a[0]) = std::move(_r); }  break;
        case 1: { ERRORET _r = _t->slot_tcp_disconnect((*reinterpret_cast< QHostAddress(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2])),(*reinterpret_cast< SOCKETMODE(*)>(_a[3])));
            if (_a[0]) *reinterpret_cast< ERRORET*>(_a[0]) = std::move(_r); }  break;
        case 2: _t->slot_tcpserver_init((*reinterpret_cast< const QHostAddress(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2])),(*reinterpret_cast< ERRORET*(*)>(_a[3]))); break;
        case 3: _t->slot_tcpserver_close((*reinterpret_cast< ERRORET*(*)>(_a[1]))); break;
        case 4: _t->slot_tcpclient_init((*reinterpret_cast< const QHostAddress(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2])),(*reinterpret_cast< ERRORET*(*)>(_a[3]))); break;
        case 5: _t->slot_tcpclient_close((*reinterpret_cast< const QHostAddress(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2])),(*reinterpret_cast< ERRORET*(*)>(_a[3]))); break;
        case 6: _t->slot_tcpclient_closeAll((*reinterpret_cast< ERRORET*(*)>(_a[1]))); break;
        case 7: _t->slot_tcpWrite_s2c((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2])),(*reinterpret_cast< QByteArray(*)>(_a[3])),(*reinterpret_cast< ERRORET*(*)>(_a[4]))); break;
        case 8: _t->slot_tcpWrite_c2s((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< quint16(*)>(_a[2])),(*reinterpret_cast< QByteArray(*)>(_a[3])),(*reinterpret_cast< ERRORET*(*)>(_a[4]))); break;
        case 9: _t->slot_Host_WakeUp((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< ERRORET*(*)>(_a[2]))); break;
        case 10: _t->slot_Host_Ping((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< ERRORET*(*)>(_a[2]))); break;
        default: ;
        }
    }
}

const QMetaObject Workmodel::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_Workmodel.data,
      qt_meta_data_Workmodel,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *Workmodel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *Workmodel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_Workmodel.stringdata0))
        return static_cast<void*>(const_cast< Workmodel*>(this));
    return QObject::qt_metacast(_clname);
}

int Workmodel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 11;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
