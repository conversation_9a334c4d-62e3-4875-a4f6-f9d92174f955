﻿#ifndef JAMMING_DECISION_H
#define JAMMING_DECISION_H

#include "rknn_inference.h"

#ifdef __cplusplus
extern "C" {
#endif


// 干扰类型枚举
typedef enum {
    JAMMING_NONE = 0,           // 无干扰
    JAMMING_COMB_SPECTRUM = 1,  // 梳状谱
    JAMMING_ISRJ = 2,          // 间歇采样转发
    JAMMING_BROADBAND = 3,     // 宽带阻塞噪声
    JAMMING_SMART_NOISE = 4,   // 灵巧噪声
    JAMMING_DRAG = 5           // 拖引
} JammingType;

// 干扰决策器内部结构
typedef struct {
    int initialized;
    double combination_threshold;  // 组合干扰阈值
    double effectiveness_weights[6]; // 各种干扰类型效果权重
} JammingDecisionImpl;

// 干扰决策结果
typedef struct JammingDecisionResult {
    int jamming_count;          // 干扰类型数量
    JammingType jamming_types[4]; // 干扰类型数组
    double jamming_probs[4];    // 干扰类型概率
    char strategy_description[256]; // 策略描述
    double confidence;          // 决策置信度
    int use_combination;        // 是否使用组合干扰
    int success;               // 处理是否成功
    double processing_time_ms; // 处理时间(毫秒)
    char error_message[256];   // 错误信息
    float* standard_output;    // 标准输出
    int output_length;         // 输出长度
} JammingDecisionResult;

// 干扰决策器句柄
typedef void* JammingDecision;

// 核心函数
JammingDecision* jamming_decision_create(void);
void jamming_decision_destroy(JammingDecision* decision);
int jamming_decision_decide(JammingDecision* decision,
                           int threat_level,
                           double threat_value,
                           const RKNNOutput* rknn_output,
                           int work_mode,
                           JammingDecisionResult* result);

// 辅助函数
const char* jamming_get_type_name(JammingType type);
const char* get_jamming_type_name_c(int type);
int jamming_is_combination_recommended(const RKNNOutput* rknn_output,
                                      int primary_type,
                                      double primary_prob);
double jamming_calculate_effectiveness(JammingType type,
                                     int threat_level,
                                     double threat_value);

#ifdef __cplusplus
}
#endif

#endif // JAMMING_DECISION_H
