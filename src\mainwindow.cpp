﻿#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "workmodel.h"
#include "qtimer.h"

#include "qt_main_c.h"

MainWindow::MainWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::MainWindow)
{
    ui->setupUi(this);

//    connect(&g_, &globalMannager::sig_run, this, &MainWindow::slot_run);

    // 初始化模型路径
//    const char* model_path = "/home/<USER>/rknn/QT_C_RKNN/models/jamming_model_ppo.rknn";

//    //timer
    t = new QTimer;
    QObject::connect(t, &QTimer::timeout, this, [](){
        run_complete_test_scenario( );
//        send_mixed_format_data();  // 使用新的混合格式数据发送函数
    });

    t->setTimerType(Qt::PreciseTimer);
    t->setInterval(10);

}

MainWindow::~MainWindow()
{
    t->stop();
    delete  t;

    delete ui;
}

void MainWindow::on_pushButton_test_clicked()
{
    static char date[]={0x01,0x02};
    emit g_.sig_datasend_s(date,2);
}

void MainWindow::slot_run()
{
//    g_.ntimer.start();
//    run_complete_test_scenario( );
//    send_mixed_format_data();  // 使用新的混合格式数据发送函数
//    qDebug()<<__FUNCTION__<<g_.ntimer.elapsed();
}
