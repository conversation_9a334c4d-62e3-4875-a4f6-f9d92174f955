/*
 * 威胁评估模块 - C语言实现
 *
 * 本模块与 simplified_threat_evaluator.py 保持一致：
 * - 威胁等级阈值：[0.792, 0.655, 0.421, 0.210]
 * - 威胁等级描述：一级威胁（终末制导）到五级威胁（雷达未激活）
 * - 频率威胁评估：基于X/C/S/L波段分类
 * - 工作模式威胁：搜索(0.2) -> 跟踪(0.5) -> 制导(0.8) -> 终末制导(1.0)
 */

#include "threat_evaluator.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>




ThreatEvaluator* threat_evaluator_create(void) {
    return threat_evaluator_create_with_params(0, 30.0, 300.0);  // 默认参数
}

ThreatEvaluator* threat_evaluator_create_with_params(int enable_radar_platform, double l1, double l2) {
    ThreatEvaluatorImpl* impl = (ThreatEvaluatorImpl*)malloc(sizeof(ThreatEvaluatorImpl));
    if (!impl) {
        return NULL;
    }

    impl->initialized = 1;
    impl->enable_radar_platform = enable_radar_platform;
    impl->l1 = l1;  // 最大威胁程度对应距离
    impl->l2 = l2;  // 最小威胁程度对应距离

    // 设置权重
    if (enable_radar_platform) {
        // 启用雷达平台时的9个参数权重
        impl->radar_type_weight = 0.08;
        impl->platform_type_weight = 0.07;
        impl->speed_weight = 0.12;
        impl->distance_weight = 0.15;
        impl->direction_weight = 0.10;
        impl->prf_weight = 0.13;
        impl->frequency_weight = 0.14;
        impl->pulse_width_weight = 0.11;
        impl->mode_weight = 0.10;
    } else {
        // 不启用雷达平台时的7个参数权重
        impl->radar_type_weight = 0.0;
        impl->platform_type_weight = 0.0;
        impl->speed_weight = 0.15;
        impl->distance_weight = 0.20;
        impl->direction_weight = 0.12;
        impl->prf_weight = 0.18;
        impl->frequency_weight = 0.18;
        impl->pulse_width_weight = 0.12;
        impl->mode_weight = 0.05;
    }

    // 功率权重（C版本特有）
    impl->power_weight = 0.0;

    return (ThreatEvaluator*)impl;
}


void threat_evaluator_destroy(ThreatEvaluator* evaluator) {
    if (evaluator) {
        free(evaluator);

    }
}



int threat_validate_radar_params(const RadarParameters* params) {
    if (!params) return 0;

    // 基础参数验证（与Python版本对应）
    if (params->frequency_mhz < 100 || params->frequency_mhz > 50000) return 0;  // 100MHz - 50GHz
    if (params->pulse_width_us < 0.01 || params->pulse_width_us > 1000) return 0;  // 0.01μs - 1ms
    if (params->prt_us < 1.0 || params->prt_us > 10000) return 0;  // 1μs - 10ms
    if (params->power_w < 0 || params->power_w > 100000000) return 0;  // 0W - 100MW
    if (params->distance_km < 0 || params->distance_km > 2000) return 0;  // 0 - 2000km
    if (params->speed_ms < 0 || params->speed_ms > 3000) return 0;  // 0 - 3000m/s
    if (params->direction_deg < -180 || params->direction_deg > 180) return 0;  // -180° - 180°
    if (params->work_mode < 0 || params->work_mode > 4) return 0;  // 0-4

    // 扩展参数验证
//    if (params->radar_type < 0 || params->radar_type > 10) return 0;  // 雷达类型
//    if (params->platform_type < 0 || params->platform_type > 10) return 0;  // 平台类型

    return 1;
}


// 雷达类型威胁评估（与Python版本对应）
static double calculate_radar_type_threat(int radar_type) {
    switch (radar_type) {
        case 1: return 0.3;  // 搜索雷达
        case 2: return 0.6;  // 跟踪雷达
        case 3: return 0.8;  // 火控雷达
        case 4: return 0.9;  // 制导雷达
        default: return 0.2;
    }
}

// 平台类型威胁评估（与Python版本对应）
static double calculate_platform_type_threat(int platform_type) {
    // 简化实现，实际应该根据具体的平台类型威胁度表
    switch (platform_type) {
        case 1: return 0.2;  // 地面平台
        case 2: return 0.5;  // 舰载平台
        case 3: return 0.7;  // 机载平台
        case 4: return 0.9;  // 导弹平台
        default: return 0.3;
    }
}

// 速度威胁评估（与Python版本对应）
static double calculate_speed_threat(double speed_ms) {
    // 基于Python版本的速度威胁度计算
    if (speed_ms >= 300.0) {
        return 0.9;  // 高速目标（超音速）
    } else if (speed_ms >= 200.0) {
        return 0.7;  // 中高速目标
    } else if (speed_ms >= 100.0) {
        return 0.5;  // 中速目标
    } else if (speed_ms >= 50.0) {
        return 0.3;  // 低速目标
    } else {
        return 0.1;  // 极低速或静止目标
    }
}

// 距离威胁评估（与Python版本对应，使用l1和l2参数）
static double calculate_distance_threat_advanced(double distance_km, double l1, double l2) {
    // 基于Python版本的距离威胁度计算
    if (distance_km <= l1) {
        return 0.9;  // 极近距离，高威胁
    } else if (distance_km >= l2) {
        return 0.1;  // 极远距离，低威胁
    } else {
        // 线性插值
        double ratio = (l2 - distance_km) / (l2 - l1);
        return 0.1 + ratio * 0.8;  // 0.1 到 0.9 之间线性变化
    }
}

// 航向威胁评估（与Python版本对应）
static double calculate_direction_threat(double direction_deg) {
    // 基于航向角的威胁度评估
    double abs_direction = fabs(direction_deg);

    if (abs_direction <= 30.0) {
        return 0.9;  // 正面接近，高威胁
    } else if (abs_direction <= 90.0) {
        return 0.6;  // 侧面接近，中等威胁
    } else if (abs_direction <= 150.0) {
        return 0.3;  // 斜后方，低威胁
    } else {
        return 0.1;  // 背离，极低威胁
    }
}

// 脉冲重复频率威胁评估（与Python版本对应）
static double calculate_prf_threat(double prt_us) {
    // 从PRT转换为PRF
    double prf_hz = 1e6 / prt_us;  // μs -> Hz

    if (prf_hz >= 5000.0) {
        return 0.9;  // 高PRF，高威胁
    } else if (prf_hz >= 1000.0) {
        return 0.6;  // 中PRF，中等威胁
    } else if (prf_hz >= 100.0) {
        return 0.3;  // 低PRF，低威胁
    } else {
        return 0.1;  // 极低PRF，极低威胁
    }
}

// 脉宽威胁评估（与Python版本对应）
static double calculate_pulse_width_threat(double pulse_width_us) {
    if (pulse_width_us <= 1.0) {
        return 0.8;  // 短脉冲，高威胁（精确制导）
    } else if (pulse_width_us <= 5.0) {
        return 0.6;  // 中等脉冲，中等威胁
    } else if (pulse_width_us <= 10.0) {
        return 0.4;  // 长脉冲，低威胁
    } else {
        return 0.2;  // 极长脉冲，极低威胁
    }
}

static double calculate_frequency_threat(double frequency_mhz) {
    // 频率威胁评估
    // X波段 (8-12 GHz): 高威胁
    if (frequency_mhz >= 8000 && frequency_mhz <= 12000) {
        return 0.9;
    }
    // C波段 (4-8 GHz): 中高威胁
    else if (frequency_mhz >= 4000 && frequency_mhz < 8000) {
        return 0.7;
    }
    // S波段 (2-4 GHz): 中等威胁
    else if (frequency_mhz >= 2000 && frequency_mhz < 4000) {
        return 0.5;
    }
    // L波段 (1-2 GHz): 低威胁
    else if (frequency_mhz >= 1000 && frequency_mhz < 2000) {
        return 0.3;
    }
    // 其他频段: 极低威胁
    else {
        return 0.1;
    }
}


static double calculate_power_threat(double power_w) {

    double log_power = log10(power_w);
    double normalized = (log_power - 3.0) / 4.0;
    return fmax(0.0, fmin(1.0, normalized));
}



static double calculate_mode_threat(int work_mode) {
    // 工作模式威胁评估（与Python版本对应）
    switch (work_mode) {
        case 0: return 0.1;   // 静默模式
        case 1: return 0.2;   // 搜索模式
        case 2: return 0.5;   // 跟踪模式
        case 3: return 0.8;   // 制导模式
        case 4: return 1.0;   // 终末制导模式
        default: return 0.2;  // 默认为搜索模式威胁度
    }
}

int threat_evaluator_assess(ThreatEvaluator* evaluator, 
                           const RadarParameters* radar_params,
                           ThreatAssessmentData* result) {
    
    ThreatEvaluatorImpl* impl = (ThreatEvaluatorImpl*)evaluator;

    

    memset(result, 0, sizeof(ThreatAssessmentData));

    // 计算所有威胁度项
    if (impl->enable_radar_platform) {
//        result->radar_type_threat = calculate_radar_type_threat(radar_params->radar_type);
//        result->platform_type_threat = calculate_platform_type_threat(radar_params->platform_type);
    } else {
        result->radar_type_threat = 0.0;
        result->platform_type_threat = 0.0;
    }

    result->speed_threat = calculate_speed_threat(radar_params->speed_ms);
    result->distance_threat = calculate_distance_threat_advanced(radar_params->distance_km, impl->l1, impl->l2);
    result->direction_threat = calculate_direction_threat(radar_params->direction_deg);
    result->prf_threat = calculate_prf_threat(radar_params->prt_us);
    result->frequency_threat = calculate_frequency_threat(radar_params->frequency_mhz);
    result->pulse_width_threat = calculate_pulse_width_threat(radar_params->pulse_width_us);
    result->mode_threat = calculate_mode_threat(radar_params->work_mode);

    // 保留功率威胁度
    result->power_threat = calculate_power_threat(radar_params->power_w);

    // 计算综合威胁度值
    result->threat_value =
        result->radar_type_threat * impl->radar_type_weight +
        result->platform_type_threat * impl->platform_type_weight +
        result->speed_threat * impl->speed_weight +
        result->distance_threat * impl->distance_weight +
        result->direction_threat * impl->direction_weight +
        result->prf_threat * impl->prf_weight +
        result->frequency_threat * impl->frequency_weight +
        result->pulse_width_threat * impl->pulse_width_weight +
        result->mode_threat * impl->mode_weight +
        result->power_threat * impl->power_weight;
    

    result->threat_value = fmax(0.0, fmin(1.0, result->threat_value));
    

    // 威胁等级判断
    if (result->threat_value > 0.792 && result->threat_value <= 1.000) {
        result->threat_level = 1;  // 一级威胁（极高威胁）
    } else if (result->threat_value > 0.655 && result->threat_value <= 0.792) {
        result->threat_level = 2;  // 二级威胁（高威胁）
    } else if (result->threat_value > 0.421 && result->threat_value <= 0.655) {
        result->threat_level = 3;  // 三级威胁（中等威胁）
    } else if (result->threat_value > 0.210 && result->threat_value <= 0.421) {
        result->threat_level = 4;  // 四级威胁（低威胁）
    } else if (result->threat_value >= 0.000 && result->threat_value <= 0.210) {
        result->threat_level = 5;  // 五级威胁（极低威胁）
    } else {
        result->threat_level = 5;  // 默认为极低威胁
    }
    

    double param_completeness = 1.0;
    double consistency_score = 1.0 - fabs(result->threat_value - 
        (result->frequency_threat + result->power_threat + 
         result->distance_threat + result->mode_threat) / 4.0);
    
    result->confidence = param_completeness * consistency_score * 0.9;
    result->confidence = fmax(0.5, fmin(1.0, result->confidence));


    result->priority = result->threat_value * result->confidence;
    result->priority = fmax(0.0, fmin(1.0, result->priority));


    
    return 0;
}


const char* threat_get_level_description(int threat_level) {
    switch (threat_level) {
        case 1: return "一级威胁（终末制导）";
        case 2: return "二级威胁（成像模式）";
        case 3: return "三级威胁（搜索转跟踪）";
        case 4: return "四级威胁（雷达搜索模式）";
        case 5: return "五级威胁（雷达未激活）";
        default: return "未知威胁等级";
    }
}


double threat_calculate_composite_score(const ThreatAssessmentData* data) {
    if (!data) return 0.0;
    
    return (data->frequency_threat + data->power_threat + 
            data->distance_threat + data->mode_threat) / 4.0;
}
