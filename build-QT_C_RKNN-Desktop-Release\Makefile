#############################################################################
# Makefile for building: ../bin/QT_C_RKNN
# Generated by qmake (3.1) (Qt 5.12.8)
# Project:  ../pro/QT_C_RKNN.pro
# Template: app
# Command: /usr/lib/qt5/bin/qmake -o Makefile ../pro/QT_C_RKNN.pro -spec linux-g++
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DQT_DEPRECATED_WARNINGS -DHAS_RKNN=1 -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -O2 -Wall -W -D_REENTRANT -fPIC $(DEFINES)
CXXFLAGS      = -pipe -O2 -Wall -W -D_REENTRANT -fPIC $(DEFINES)
INCPATH       = -I../pro -I. -I../inc -isystem /usr/lib/gcc/aarch64-linux-gnu/9/include -I../workmodel -I../../QT_C_RKNN -isystem /usr/include/aarch64-linux-gnu/qt5 -isystem /usr/include/aarch64-linux-gnu/qt5/QtWidgets -isystem /usr/include/aarch64-linux-gnu/qt5/QtGui -isystem /usr/include/aarch64-linux-gnu/qt5/QtNetwork -isystem /usr/include/aarch64-linux-gnu/qt5/QtCore -I. -I. -I/usr/lib/aarch64-linux-gnu/qt5/mkspecs/linux-g++
QMAKE         = /usr/lib/qt5/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /usr/lib/qt5/bin/qmake -install qinstall
QINSTALL_PROGRAM = /usr/lib/qt5/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = QT_C_RKNN1.0.0
DISTDIR = /home/<USER>/rknn/QT_C_RKNN/build-QT_C_RKNN-Desktop-Release/.tmp/QT_C_RKNN1.0.0
LINK          = g++
LFLAGS        = -Wl,-O1
LIBS          = $(SUBLIBS) -L/home/<USER>/rknn/QT_C_RKNN/pro/../lib/ -lrknnrt /usr/lib/aarch64-linux-gnu/libQt5Widgets.so /usr/lib/aarch64-linux-gnu/libQt5Gui.so /usr/lib/aarch64-linux-gnu/libQt5Network.so /usr/lib/aarch64-linux-gnu/libQt5Core.so /usr/lib/aarch64-linux-gnu/libGL.so -lpthread   
AR            = ar cqs
RANLIB        = 
SED           = sed
STRIP         = strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = ../src/main.cpp \
		../src/mainwindow.cpp \
		../src/qt_main_c.cpp \
		../src/jamming_decision.c \
		../src/rknn_inference.c \
		../src/threat_evaluator.c \
		../workmodel/workmodel.cpp moc_mainwindow.cpp \
		moc_workmodel.cpp
OBJECTS       = main.o \
		mainwindow.o \
		qt_main_c.o \
		jamming_decision.o \
		rknn_inference.o \
		threat_evaluator.o \
		workmodel.o \
		moc_mainwindow.o \
		moc_workmodel.o
DIST          = /usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/spec_pre.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/unix.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/linux.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/sanitize.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/gcc-base.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/gcc-base-unix.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/g++-base.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/g++-unix.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/qconfig.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_core.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_core_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_edid_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_egl_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_fb_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_glx_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_input_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_kms_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_network.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_network_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_service_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_theme_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/qt_functions.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/qt_config.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/linux-g++/qmake.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/spec_post.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/exclusive_builds.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/toolchain.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/default_pre.prf \
		../workmodel/workmodel.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/resolve_config.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/default_post.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/warn_on.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/qt.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/resources.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/moc.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/unix/opengl.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/uic.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/unix/thread.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/qmake_use.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/file_copies.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/testcase_targets.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/exceptions.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/yacc.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/lex.prf \
		../pro/QT_C_RKNN.pro ../inc/jamming_decision.h \
		../inc/mainwindow.h \
		../inc/qt_main_c.h \
		../inc/rknn_api.h \
		../inc/rknn_inference.h \
		../inc/threat_evaluator.h \
		../workmodel/workmodel.h \
		../workmodel/workmodelrely.h ../src/main.cpp \
		../src/mainwindow.cpp \
		../src/qt_main_c.cpp \
		../src/jamming_decision.c \
		../src/rknn_inference.c \
		../src/threat_evaluator.c \
		../workmodel/workmodel.cpp
QMAKE_TARGET  = QT_C_RKNN
DESTDIR       = ../bin/
TARGET        = ../bin/QT_C_RKNN


first: all
####### Build rules

../bin/QT_C_RKNN: ui_mainwindow.h $(OBJECTS)  
	@test -d ../bin/ || mkdir -p ../bin/
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: ../pro/QT_C_RKNN.pro /usr/lib/aarch64-linux-gnu/qt5/mkspecs/linux-g++/qmake.conf /usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/spec_pre.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/unix.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/linux.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/sanitize.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/gcc-base.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/gcc-base-unix.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/g++-base.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/g++-unix.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/qconfig.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_core.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_core_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_edid_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_egl_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_fb_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_glx_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_input_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_kms_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_network.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_network_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_service_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_theme_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml_private.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/qt_functions.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/qt_config.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/linux-g++/qmake.conf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/spec_post.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/exclusive_builds.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/toolchain.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/default_pre.prf \
		../workmodel/workmodel.pri \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/resolve_config.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/default_post.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/warn_on.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/qt.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/resources.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/moc.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/unix/opengl.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/uic.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/unix/thread.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/qmake_use.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/file_copies.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/testcase_targets.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/exceptions.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/yacc.prf \
		/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/lex.prf \
		../pro/QT_C_RKNN.pro
	$(QMAKE) -o Makefile ../pro/QT_C_RKNN.pro -spec linux-g++
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/spec_pre.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/unix.conf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/linux.conf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/sanitize.conf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/gcc-base.conf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/gcc-base-unix.conf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/g++-base.conf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/common/g++-unix.conf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/qconfig.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_accessibility_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_bootstrap_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_core.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_core_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_edid_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_egl_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_fb_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_glx_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_input_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_kms_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_network.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_network_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_service_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_theme_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_vulkan_support_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml_private.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/qt_functions.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/qt_config.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/linux-g++/qmake.conf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/spec_post.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/exclusive_builds.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/toolchain.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/default_pre.prf:
../workmodel/workmodel.pri:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/resolve_config.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/default_post.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/warn_on.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/qt.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/resources.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/moc.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/unix/opengl.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/uic.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/unix/thread.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/qmake_use.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/file_copies.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/testcase_targets.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/exceptions.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/yacc.prf:
/usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/lex.prf:
../pro/QT_C_RKNN.pro:
qmake: FORCE
	@$(QMAKE) -o Makefile ../pro/QT_C_RKNN.pro -spec linux-g++

qmake_all: FORCE


all: Makefile ../bin/QT_C_RKNN

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents /usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/data/dummy.cpp $(DISTDIR)/
	$(COPY_FILE) --parents ../inc/jamming_decision.h ../inc/mainwindow.h ../inc/qt_main_c.h ../inc/rknn_api.h ../inc/rknn_inference.h ../inc/threat_evaluator.h ../workmodel/workmodel.h ../workmodel/workmodelrely.h $(DISTDIR)/
	$(COPY_FILE) --parents ../src/main.cpp ../src/mainwindow.cpp ../src/qt_main_c.cpp ../src/jamming_decision.c ../src/rknn_inference.c ../src/threat_evaluator.c ../workmodel/workmodel.cpp $(DISTDIR)/
	$(COPY_FILE) --parents ../ui/mainwindow.ui $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) Makefile


####### Sub-libraries

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) moc_predefs.h
moc_predefs.h: /usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/data/dummy.cpp
	g++ -pipe -O2 -Wall -W -dM -E -o moc_predefs.h /usr/lib/aarch64-linux-gnu/qt5/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: moc_mainwindow.cpp moc_workmodel.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_mainwindow.cpp moc_workmodel.cpp
moc_mainwindow.cpp: ../inc/mainwindow.h \
		moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/rknn/QT_C_RKNN/build-QT_C_RKNN-Desktop-Release/moc_predefs.h -I/usr/lib/aarch64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/rknn/QT_C_RKNN/pro -I/home/<USER>/rknn/QT_C_RKNN/inc -I/usr/lib/gcc/aarch64-linux-gnu/9/include -I/home/<USER>/rknn/QT_C_RKNN/workmodel -I/home/<USER>/rknn/QT_C_RKNN -I/usr/include/aarch64-linux-gnu/qt5 -I/usr/include/aarch64-linux-gnu/qt5/QtWidgets -I/usr/include/aarch64-linux-gnu/qt5/QtGui -I/usr/include/aarch64-linux-gnu/qt5/QtNetwork -I/usr/include/aarch64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/9 -I/usr/include/aarch64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/aarch64-linux-gnu/9/include -I/usr/local/include -I/usr/include/aarch64-linux-gnu -I/usr/include ../inc/mainwindow.h -o moc_mainwindow.cpp

moc_workmodel.cpp: ../workmodel/workmodel.h \
		../workmodel/workmodelrely.h \
		moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/rknn/QT_C_RKNN/build-QT_C_RKNN-Desktop-Release/moc_predefs.h -I/usr/lib/aarch64-linux-gnu/qt5/mkspecs/linux-g++ -I/home/<USER>/rknn/QT_C_RKNN/pro -I/home/<USER>/rknn/QT_C_RKNN/inc -I/usr/lib/gcc/aarch64-linux-gnu/9/include -I/home/<USER>/rknn/QT_C_RKNN/workmodel -I/home/<USER>/rknn/QT_C_RKNN -I/usr/include/aarch64-linux-gnu/qt5 -I/usr/include/aarch64-linux-gnu/qt5/QtWidgets -I/usr/include/aarch64-linux-gnu/qt5/QtGui -I/usr/include/aarch64-linux-gnu/qt5/QtNetwork -I/usr/include/aarch64-linux-gnu/qt5/QtCore -I. -I/usr/include/c++/9 -I/usr/include/aarch64-linux-gnu/c++/9 -I/usr/include/c++/9/backward -I/usr/lib/gcc/aarch64-linux-gnu/9/include -I/usr/local/include -I/usr/include/aarch64-linux-gnu -I/usr/include ../workmodel/workmodel.h -o moc_workmodel.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h
ui_mainwindow.h: ../ui/mainwindow.ui \
		/usr/lib/qt5/bin/uic
	/usr/lib/qt5/bin/uic ../ui/mainwindow.ui -o ui_mainwindow.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 

####### Compile

main.o: ../src/main.cpp ../inc/mainwindow.h \
		../workmodel/workmodel.h \
		../workmodel/workmodelrely.h \
		../inc/qt_main_c.h \
		../inc/threat_evaluator.h \
		../inc/rknn_inference.h \
		../inc/rknn_api.h \
		../inc/jamming_decision.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o ../src/main.cpp

mainwindow.o: ../src/mainwindow.cpp ../inc/mainwindow.h \
		ui_mainwindow.h \
		../workmodel/workmodel.h \
		../workmodel/workmodelrely.h \
		../inc/qt_main_c.h \
		../inc/threat_evaluator.h \
		../inc/rknn_inference.h \
		../inc/rknn_api.h \
		../inc/jamming_decision.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o mainwindow.o ../src/mainwindow.cpp

qt_main_c.o: ../src/qt_main_c.cpp ../inc/mainwindow.h \
		../workmodel/workmodel.h \
		../workmodel/workmodelrely.h \
		../inc/qt_main_c.h \
		../inc/threat_evaluator.h \
		../inc/rknn_inference.h \
		../inc/rknn_api.h \
		../inc/jamming_decision.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qt_main_c.o ../src/qt_main_c.cpp

jamming_decision.o: ../src/jamming_decision.c ../inc/jamming_decision.h \
		../inc/rknn_inference.h \
		../inc/rknn_api.h
	$(CC) -c $(CFLAGS) $(INCPATH) -o jamming_decision.o ../src/jamming_decision.c

rknn_inference.o: ../src/rknn_inference.c ../inc/rknn_inference.h \
		../inc/rknn_api.h
	$(CC) -c $(CFLAGS) $(INCPATH) -o rknn_inference.o ../src/rknn_inference.c

threat_evaluator.o: ../src/threat_evaluator.c ../inc/threat_evaluator.h
	$(CC) -c $(CFLAGS) $(INCPATH) -o threat_evaluator.o ../src/threat_evaluator.c

workmodel.o: ../workmodel/workmodel.cpp ../workmodel/workmodel.h \
		../workmodel/workmodelrely.h \
		../inc/threat_evaluator.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o workmodel.o ../workmodel/workmodel.cpp

moc_mainwindow.o: moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_mainwindow.o moc_mainwindow.cpp

moc_workmodel.o: moc_workmodel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_workmodel.o moc_workmodel.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

