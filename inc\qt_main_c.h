#ifndef QT_MAIN_C_H
#define QT_MAIN_C_H

#ifdef __cplusplus
extern "C" {
#endif

#include "threat_evaluator.h"
#include "rknn_inference.h"
#include "jamming_decision.h"

// 混合数据类型结构（支持int和float）
typedef struct {
    enum { TYPE_INT, TYPE_FLOAT } type;
    union {
        int int_val;
        float float_val;
    } value;
} MixedData;

// 全局变量声明
extern ThreatAssessmentData g_latest_threat_data;
extern JammingDecisionResult g_latest_jamming_result;
extern RKNNOutput* g_current_rknn_output;

// 系统初始化和销毁函数
int initialize_all_modules(const char* model_path);
void destroy_all_modules(void);

// 测试场景函数
void run_complete_test_scenario();

// 雷达输入模拟函数
void simulate_radar_input(RadarParameters* radar, int scenario);

// 结果显示函数
void display_complete_result(const ThreatAssessmentData* threat_data,
                           const JammingDecisionResult* jamming_result);

// 唯一保留的输出函数（按表格规范，支持int和float混合数据）
void output_mixed_jamming_format(const ThreatAssessmentData* threat_data,
                                const JammingDecisionResult* jamming_result);

// 混合格式数据生成函数（按表格规范）
int generate_standard_mixed_output(const ThreatAssessmentData* threat_data,
                                  const JammingDecisionResult* jamming_result,
                                  const RKNNOutput* rknn_output,
                                  MixedData* output, int max_size);

// 根据干扰类型添加混合参数（完全按表格规范）
int add_mixed_jamming_params_by_type(int jamming_id, double prob, const RKNNOutput* rknn_output,
                                    MixedData* output, int start_idx, int max_size);

// 辅助函数
const char* get_jamming_type_name_by_id(int jamming_id);
const char* get_bandwidth_description(int bandwidth_id);

// 数据发送函数（按标准格式排序发送，int和float都是4字节）
void send_mixed_format_data();
void send_mixed_format_data_to_network(const MixedData* mixed_output, int output_size);

// 测试函数
void test_mixed_data_output();

#ifdef __cplusplus
}
#endif

#endif // QT_MAIN_C_H
