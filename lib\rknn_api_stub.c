﻿/* RKNN API Stub Implementation for Development */
#include "rknn_api.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

/* Stub implementation for development when real RKNN library is not available */

int rknn_init(rknn_context* ctx, void* model, uint32_t size, uint32_t flag, rknn_init_extend* extend) {
    printf("RKNN Stub: rknn_init called\n");
    *ctx = (rknn_context)0x12345678;  /* Fake context */
    return RKNN_SUCC;
}

int rknn_destroy(rknn_context ctx) {
    printf("RKNN Stub: rknn_destroy called\n");
    return RKNN_SUCC;
}

int rknn_query(rknn_context ctx, rknn_query_cmd cmd, void* info, uint32_t size) {
    printf("RKNN Stub: rknn_query called, cmd=%d\n", cmd);
    
    switch (cmd) {
        case RKNN_QUERY_IN_OUT_NUM: {
            rknn_input_output_num* io_num = (rknn_input_output_num*)info;
            io_num->n_input = 1;
            io_num->n_output = 11;  /* Match ONNX model outputs */
            break;
        }
        case RKNN_QUERY_INPUT_ATTR: {
            rknn_tensor_attr* attr = (rknn_tensor_attr*)info;
            attr->index = 0;
            attr->n_dims = 2;
            attr->dims[0] = 1;
            attr->dims[1] = 12;
            attr->n_elems = 12;
            attr->size = 12 * sizeof(float);
            attr->type = RKNN_TENSOR_FLOAT32;
            attr->fmt = RKNN_TENSOR_NCHW;
            strcpy(attr->name, "input");
            break;
        }
        case RKNN_QUERY_OUTPUT_ATTR: {
            rknn_tensor_attr* attr = (rknn_tensor_attr*)info;
            /* Simulate different output sizes based on index */
            if (attr->index == 0) {
                /* jamming_type_probs: [1, 5] */
                attr->n_dims = 2;
                attr->dims[0] = 1;
                attr->dims[1] = 5;
                attr->n_elems = 5;
                attr->size = 5 * sizeof(float);
                strcpy(attr->name, "jamming_type_probs");
            } else if (attr->index == 1) {
                /* combination_scores: [1, 1] */
                attr->n_dims = 2;
                attr->dims[0] = 1;
                attr->dims[1] = 1;
                attr->n_elems = 1;
                attr->size = 1 * sizeof(float);
                strcpy(attr->name, "combination_scores");
            } else {
                /* Other outputs: variable sizes */
                attr->n_dims = 2;
                attr->dims[0] = 1;
                attr->dims[1] = 6;  /* Default 6 parameters */
                attr->n_elems = 6;
                attr->size = 6 * sizeof(float);
                sprintf(attr->name, "output_%d", attr->index);
            }
            attr->type = RKNN_TENSOR_FLOAT32;
            attr->fmt = RKNN_TENSOR_NCHW;
            break;
        }
        default:
            return RKNN_ERR_PARAM_INVALID;
    }
    
    return RKNN_SUCC;
}

int rknn_inputs_set(rknn_context ctx, uint32_t n_inputs, rknn_input inputs[]) {
    printf("RKNN Stub: rknn_inputs_set called, n_inputs=%d\n", n_inputs);
    return RKNN_SUCC;
}

int rknn_run(rknn_context ctx, rknn_run_extend* extend) {
    printf("RKNN Stub: rknn_run called\n");
    return RKNN_SUCC;
}

int rknn_outputs_get(rknn_context ctx, uint32_t n_outputs, rknn_output outputs[], rknn_run_extend* extend) {
    printf("RKNN Stub: rknn_outputs_get called, n_outputs=%d\n", n_outputs);
    
    /* Allocate and fill fake output data */
    for (uint32_t i = 0; i < n_outputs; i++) {
        if (i == 0) {
            /* jamming_type_probs: 5 values */
            outputs[i].size = 5 * sizeof(float);
            outputs[i].buf = malloc(outputs[i].size);
            float* data = (float*)outputs[i].buf;
            data[0] = 0.2f;  /* Comb jamming */
            data[1] = 0.3f;  /* ISRJ jamming */
            data[2] = 0.1f;  /* Broadband noise */
            data[3] = 0.25f; /* Smart noise */
            data[4] = 0.15f; /* Deception */
        } else if (i == 1) {
            /* combination_scores: 1 value */
            outputs[i].size = 1 * sizeof(float);
            outputs[i].buf = malloc(outputs[i].size);
            float* data = (float*)outputs[i].buf;
            data[0] = 0.1f;  /* Low combination score */
        } else {
            /* Other outputs: 6 parameters each */
            outputs[i].size = 6 * sizeof(float);
            outputs[i].buf = malloc(outputs[i].size);
            float* data = (float*)outputs[i].buf;
            for (int j = 0; j < 6; j++) {
                data[j] = 0.5f + (float)j * 0.1f;  /* Fake parameters */
            }
        }
    }
    
    return RKNN_SUCC;
}

int rknn_outputs_release(rknn_context ctx, uint32_t n_outputs, rknn_output outputs[]) {
    printf("RKNN Stub: rknn_outputs_release called, n_outputs=%d\n", n_outputs);
    
    /* Free allocated output buffers */
    for (uint32_t i = 0; i < n_outputs; i++) {
        if (outputs[i].buf) {
            free(outputs[i].buf);
            outputs[i].buf = NULL;
        }
    }
    
    return RKNN_SUCC;
}
