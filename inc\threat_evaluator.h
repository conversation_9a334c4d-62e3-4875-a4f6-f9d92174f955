#ifndef THREAT_EVALUATOR_H
#define THREAT_EVALUATOR_H

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    int head;
    int len;
} FrameHead;

typedef struct {
    // 基础雷达参数（与Python版本对应）
    double frequency_mhz;        // 载频 (MHz)
    double pulse_width_us;       // 脉宽 (μs)
    double prt_us;              // 脉冲重复周期 (μs)
    double power_w;             // 功率 (W)
    double distance_km;         // 距离 (km)
    double speed_ms;            // 速度 (m/s)
    double direction_deg;       // 航向角 (度)
    int work_mode;              // 工作模式


//    int radar_type;             // 雷达类型
//    int platform_type;          // 平台类型
} RadarParameters;


typedef struct {
    // 主要评估结果
    int threat_level;           // 威胁等级 (1-5)
    double threat_value;        // 威胁度值 (0-1)
    double confidence;          // 置信度 (0-1)
    double priority;            // 优先级 (0-1)

    // 详细威胁评估项（与Python版本对应）
    double radar_type_threat;   // 雷达类型威胁度
    double platform_type_threat; // 平台类型威胁度
    double speed_threat;        // 速度威胁度
    double distance_threat;     // 距离威胁度
    double direction_threat;    // 航向威胁度
    double prf_threat;          // 脉冲重复频率威胁度
    double frequency_threat;    // 载频威胁度
    double pulse_width_threat;  // 脉宽威胁度
    double mode_threat;         // 工作模式威胁度

    // 保留原有的功率威胁度（C版本特有）
    double power_threat;        // 功率威胁度
} ThreatAssessmentData;

typedef struct {
    int initialized;
    int enable_radar_platform;  // 是否启用雷达平台评估

    // 权重参数（与Python版本对应）
    double radar_type_weight;
    double platform_type_weight;
    double speed_weight;
    double distance_weight;
    double direction_weight;
    double prf_weight;
    double frequency_weight;
    double pulse_width_weight;
    double mode_weight;

    // 保留原有的功率权重（C版本特有）
    double power_weight;

    // 距离参数（与Python版本对应）
    double l1;  // 最大威胁程度对应距离 (km)
    double l2;  // 最小威胁程度对应距离 (km)
} ThreatEvaluatorImpl;
// Threat evaluator handle
typedef void* ThreatEvaluator;


// 创建和销毁函数
ThreatEvaluator* threat_evaluator_create(void);
ThreatEvaluator* threat_evaluator_create_with_params(int enable_radar_platform, double l1, double l2);
void threat_evaluator_destroy(ThreatEvaluator* evaluator);

// 威胁评估函数
int threat_evaluator_assess(ThreatEvaluator* evaluator,
                           const RadarParameters* radar_params,
                           ThreatAssessmentData* result);


const char* threat_get_level_description(int threat_level);
int threat_validate_radar_params(const RadarParameters* params);
double threat_calculate_composite_score(const ThreatAssessmentData* data);

#ifdef __cplusplus
}
#endif

#endif // THREAT_EVALUATOR_H
